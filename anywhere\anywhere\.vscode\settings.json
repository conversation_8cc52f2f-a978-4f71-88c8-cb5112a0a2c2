{"files.associations": {"*.dbclient-js": "javascript", "*.pro": "qmake", "chrono": "cpp", "filesystem": "cpp", "fstream": "cpp", "memory": "cpp", "optional": "cpp", "regex": "cpp", "system_error": "cpp", "algorithm": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "cctype": "cpp", "charconv": "cpp", "clocale": "cpp", "cmath": "cpp", "compare": "cpp", "concepts": "cpp", "condition_variable": "cpp", "cstdarg": "cpp", "cstddef": "cpp", "cstdint": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "ctime": "cpp", "cwchar": "cpp", "cwctype": "cpp", "deque": "cpp", "exception": "cpp", "format": "cpp", "forward_list": "cpp", "functional": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "ios": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "iterator": "cpp", "limits": "cpp", "list": "cpp", "locale": "cpp", "map": "cpp", "mutex": "cpp", "new": "cpp", "ostream": "cpp", "queue": "cpp", "random": "cpp", "ratio": "cpp", "set": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stack": "cpp", "stdexcept": "cpp", "stop_token": "cpp", "streambuf": "cpp", "string": "cpp", "thread": "cpp", "tuple": "cpp", "type_traits": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "utility": "cpp", "variant": "cpp", "vector": "cpp", "xfacet": "cpp", "xhash": "cpp", "xiosbase": "cpp", "xlocale": "cpp", "xlocbuf": "cpp", "xlocinfo": "cpp", "xlocmes": "cpp", "xlocmon": "cpp", "xlocnum": "cpp", "xloctime": "cpp", "xmemory": "cpp", "xstddef": "cpp", "xstring": "cpp", "xtr1common": "cpp", "xtree": "cpp", "xutility": "cpp", "*.rh": "cpp", "cassert": "cpp", "qobject": "cpp", "qdatetime": "cpp", "qtimer": "cpp", "coroutine": "cpp", "resumable": "cpp", "future": "cpp", "numeric": "cpp", "qguiapplication": "cpp", "qnetworkaccessmanager": "cpp", "qsystemtrayicon": "cpp", "qaction": "cpp", "qapplication": "cpp", "qmenu": "cpp", "qqmlapplicationengine": "cpp", "qpainter": "cpp", "qlocalserver": "cpp", "qmessagebox": "cpp", "qquickwindow": "cpp", "qstring": "cpp", "qtconcurrent": "cpp", "qdebug": "cpp", "qcursor": "cpp", "qmouseevent": "cpp", "qkeyevent": "cpp", "qtplugin": "cpp", "qquickpainteditem": "cpp", "qtwin": "cpp", "qqmlcontext": "cpp", "qlocalsocket": "cpp", "qquickitem": "cpp", "codecvt": "cpp", "qjsondocument": "cpp", "qcommandlineparser": "cpp", "qabstracttablemodel": "cpp", "qwindow": "cpp", "qicon": "cpp", "qcoreapplication": "cpp", "qprocess": "cpp", "qnetworkrequest": "cpp", "qstandardpaths": "cpp", "qvariant": "cpp", "qmimetype": "cpp", "qmutexlocker": "cpp", "qdir": "cpp", "qthread": "cpp", "qjsonarray": "cpp", "qmutex": "cpp", "qimage": "cpp", "qtquick": "cpp", "qnetworkinterface": "cpp", "qnetworkaddressentry": "cpp", "qvector": "cpp", "qjsonobject": "cpp", "qhostinfo": "cpp", "qeventloop": "cpp", "qjsonparseerror": "cpp", "qhostaddress": "cpp", "qmimedatabase": "cpp", "qmetatype": "cpp", "qnetworkreply": "cpp", "qfile": "cpp", "qstorageinfo": "cpp", "qlocale": "cpp", "qcryptographichash": "cpp", "qrunnable": "cpp", "unordered_set": "cpp", "qfileinfo": "cpp", "qurl": "cpp", "qthreadpool": "cpp", "qdomdocument": "cpp", "qsharedpointer": "cpp", "any": "cpp", "bitset": "cpp", "ranges": "cpp", "span": "cpp", "valarray": "cpp", "qcollator": "cpp", "qtextdocument": "cpp", "*.cpp_del": "cpp"}}