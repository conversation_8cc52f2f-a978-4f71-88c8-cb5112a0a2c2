<RCC>
    <qresource prefix="/bundles">
        <file>libRockeyARM.so</file>
        <file>pdf/libpdftextextractor.so</file>
        <file>docwire/libdocwiretextextractor.so</file>
        <file>7z/lib7z.so</file>
        <file>fileIcon/css.png</file>
        <file>fileIcon/docx.png</file>
        <file>fileIcon/empty.png</file>
        <file>fileIcon/folder.png</file>
        <file>fileIcon/html.png</file>
        <file>fileIcon/image.png</file>
        <file>fileIcon/js.png</file>
        <file>fileIcon/pdf.png</file>
        <file>fileIcon/pptx.png</file>
        <file>fileIcon/txt.png</file>
        <file>fileIcon/wps.png</file>
        <file>fileIcon/xlsx.png</file>
        <file>fileIcon/zip.png</file>
        <file>libmole_client_ffi.so</file>
    </qresource>
</RCC>
