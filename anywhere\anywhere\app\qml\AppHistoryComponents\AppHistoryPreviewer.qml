import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Controls.Universal 2.12
import QtQuick.Layouts 1.15
import AnywhereQmlModule 1.0
import SkyUi 1.0
import "./AppHistoryPreviewerComponents" as Components

Rectangle {
  id: previewRoot
  color: "transparent"

  property string activeFilePath

  Item {
    anchors.fill: parent

    AppHistoryPreviewViewModel {
      id: appHistoryPreviewViewModel
      activeFilePath: previewRoot.activeFilePath
      onTextStateChanged: function(textState) {
        if (appHistoryPreviewViewModel.imageState == AppHistoryPreviewViewModel.UNAVAILABLE || appHistoryPreviewViewModel.imageState == AppHistoryPreviewViewModel.INIT) {
          previewBar.currentIndex = 0
        }
        switch (textState) {
          case AppHistoryPreviewViewModel.INIT:
            textLoader.textStateStr = qsTr("暂无数据")
            break
          case AppHistoryPreviewViewModel.LOADING:
            textLoader.textStateStr = qsTr("加载中...")
            break
          case AppHistoryPreviewViewModel.UNAVAILABLE:
            textLoader.textStateStr = qsTr("不支持的格式")
            break
          case AppHistoryPreviewViewModel.NOT_EXIST:
            textLoader.textStateStr = qsTr("文件不存在")
            break
          default:
            textLoader.textStateStr = qsTr("没有提取到有效内容")
            break
        }

        textLoader.isActive = textState == AppHistoryPreviewViewModel.DONE
        textLoader.isLoading = textState == AppHistoryPreviewViewModel.LOADING
      }

      onImageStateChanged: function(imageState) {
        if (appHistoryPreviewViewModel.textState == AppHistoryPreviewViewModel.UNAVAILABLE || appHistoryPreviewViewModel.textState == AppHistoryPreviewViewModel.INIT) {
          previewBar.currentIndex = 1
        }

        switch (imageState) {
          case AppHistoryPreviewViewModel.INIT:
            imageLoader.imageStateStr = qsTr("暂无数据")
            break
          case AppHistoryPreviewViewModel.LOADING:
            imageLoader.imageStateStr = qsTr("加载中...")
            break
          case AppHistoryPreviewViewModel.DONE:
            imageLoader.imageStateStr = qsTr("完成")
            break
          case AppHistoryPreviewViewModel.NOT_EXIST:
            imageLoader.imageStateStr = qsTr("文件不存在")
            break
          default:
            imageLoader.imageStateStr = qsTr("没有预览, 当前仅支持预览图片文件")
            break
        }
        
        imageLoader.isActive = imageState == AppHistoryPreviewViewModel.DONE
        imageLoader.isLoading = imageState == AppHistoryPreviewViewModel.LOADING
      }

      
      onActiveFilePathChanged: {
        imageLoader.activeFilePath = appHistoryPreviewViewModel.activeFilePath
      }

      onImageChanged: function(image) {
        imageLoader.imageSource = image
      }

      onContentChanged: function(content) {
        textLoader.content = appHistoryPreviewViewModel.content
      }
    }

    ColumnLayout {
      anchors.fill: parent

      TabBar {
        id: previewBar
        Layout.fillWidth: true
        Layout.preferredHeight: 30
        SkyTabBar {
          title: qsTr("内容详情")
          active: previewBar.currentIndex == 0
          anchors.bottom: parent.bottom
        }
        SkyTabBar {
          title: qsTr("预览图片")
          active: previewBar.currentIndex == 1
          anchors.bottom: parent.bottom
        }
      }

      Connections {
        target: previewBar
        function onCurrentIndexChanged() {
          // 当切换标签页时, 触发相应标签页的数据加载
          if (previewBar.currentIndex == 0) {
            appHistoryPreviewViewModel.loadText();
          } else if (previewBar.currentIndex == 1) {
            appHistoryPreviewViewModel.loadImage();
          }
        }
      }

      // 内容摘要 / 预览
      StackLayout {
        Layout.fillWidth: true
        Layout.fillHeight: true
        currentIndex: previewBar.currentIndex
        // 内容摘要
        Item {
          Layout.fillWidth: true
          Layout.fillHeight: true
          Components.ShowText {
            id: textLoader
          }
        }
        // 预览图片
        Item {
          Layout.fillWidth: true
          Layout.fillHeight: true
          Components.ShowImage {
            id: imageLoader
          }
        }
      }
    }
  }
}