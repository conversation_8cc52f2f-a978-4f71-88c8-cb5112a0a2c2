import QtQuick.Controls 2.15
import QtQuick 2.12
import SkyUi 1.0


Item {
  id: imageLoader
  property string activeFilePath: ""
  property string imageSource: ""
  property string imageStateStr: ""
  property bool isLoading: false
  property bool isActive: false

  anchors.fill: parent
  Loader {
    anchors.fill: parent
    active: imageLoader.isActive
    sourceComponent: Image {
      id: image
      anchors.fill: parent
      source: imageLoader.imageSource
      asynchronous: true
      cache: false
      autoTransform: true
      fillMode: Image.PreserveAspectFit
      Button {
        anchors.fill: parent
        text: qsTr("")
        background: Rectangle {
          color: "transparent"
        }
        onDoubleClicked: {
          skyFileMenu.openFile(imageLoader.activeFilePath)
        }
      }
    }
  }

  Loader {
    anchors.fill: parent
    active: !imageLoader.isActive
    sourceComponent: SkyEmpty {
      id: imageEmpty
      anchors.fill: parent
      size: 100
      fontSize: skyTheme.fontSizeSmall
      isLoading: imageLoader.isActive
      text: imageLoader.imageStateStr
    }
  }
}