import QtQuick.Controls 2.15
import QtQuick 2.12
import AnywhereQmlModule 1.0
import SkyUi 1.0

Item {
  id: textLoader
  property string content: ""
  property string textStateStr: ""
  property bool isLoading: false
  property bool isActive: false

  anchors.fill: parent

  Connections {
    target: appHistoryPreviewViewModel
  }

  Loader {
    anchors.fill: parent
    active: textLoader.isActive
    sourceComponent: Item {
      id: textEditWrapper
      anchors.fill: parent

      QTextItem {
        id: textEdit
        width: textEditWrapper.width
        height: viewportHeight
        viewportHeight: textEditWrapper.height
        text: qsTr(textLoader.content)

        font.family: skyTheme.fontFamily
        font.pixelSize: skyTheme.fontSize
        font.letterSpacing: 2
        font.weight: Font.Thin
      }
    }
  }

  Loader {
    anchors.fill: parent
    active: !textLoader.isActive
    sourceComponent: SkyEmpty {
      id: empty
      anchors.fill: parent
      size: 100
      fontSize: skyTheme.fontSizeSmall
      isLoading: (textLoader.isLoading)
      text: qsTr(textLoader.textStateStr)
    }
  }
}