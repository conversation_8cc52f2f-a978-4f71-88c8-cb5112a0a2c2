import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Controls.Universal 2.12
import QtQuick.Layouts 1.15
import QtQuick.Controls 1.2 as QC1
import QtQuick.Controls.Styles 1.4 as QC1Styles
import AnywhereQmlModule 1.0
import SkyUi 1.0
import "AppHistoryTreeUtils.js" as Utils
import "./" as Components

Rectangle {
  id: root
  property var appHistoryData

  /**
   * @brief 当前激活的文件路径列表
   * @param {string[]} filePaths
   */
  property var selectedFilePaths_: []

  /**
   * @brief 当前激活的文件列表
   * @param {{string path, string appType}[]} AppHistory
   */
  property var selectedAppHistory_: []

  /**
   * @brief 待删除文件列表
   * @param {string[]} filePaths
   */
  signal deleteFile(var filePaths);
  color: "transparent"

  Connections {
    target: appHistoryViewModel
    function onSearchingChanged() {
      treeView.currentRow = -1
      if (!appHistoryViewModel.searching) {
        // 搜索结束 我们重新展开一次，默认全展开
        expandTree({}, true)
      }
    }
  }

  StackLayout {
    id: stackLayout
    anchors.fill: parent
    currentIndex: {
      if (appHistoryViewModel.searching || appHistoryViewModel.indexState == AppHistoryViewModel.Init) {
        return 1
      }

      if (appHistoryViewModel.indexState == AppHistoryViewModel.Done) {
        if (appHistoryViewModel.treeModel && appHistoryViewModel.treeModel.size == 0) return 1
      }
      return 0
    }

    // 有效数据
    Item {
      Layout.fillWidth: true
      Layout.fillHeight: true
      
      SkyTree {
        id: treeView
        treeModel: appHistoryViewModel.treeModel
        sortColumns: Utils.COLUMN_INFO
        focus: true
        // 用来记录是否已经展开, 当搜索 排序完成后, 我们还原展开状态
        property var expandedMap: undefined

        // 监听treeModel变化，数据加载完成后自动全展开
        Connections {
          target: appHistoryViewModel.treeModel
          function onModelReset() {
            // 延迟执行，确保UI已经更新
            Qt.callLater(function() {
              expandTree({}, true); // 全展开
            });
          }
        }

        Component.onCompleted: {
          // 组件完成后也尝试展开，作为备用方案
          Qt.callLater(function() {
            if (appHistoryViewModel.treeModel.size > 0) {
              expandTree({}, true); // 全展开
            }
          });
        }
        onOrderChnage: {
          appHistoryViewModel.treeModel.sort(role, !!order);
          // 排序结束 我们重新展开一次，默认全展开
          expandTree({}, true)
        }

        onCurrentIndexChanged: {
          const appHistory = appHistoryViewModel.treeModel.getChildrenHistory(currentIndex);
          if(appHistory.length > 0) {
            if (appHistory[0].exists){
              appHistoryViewModel.activeFilePath = appHistory[0].path;
            } else {
              appHistoryViewModel.activeFilePath = ""
            }
          }
        }

        onRightClickedRow: {
          const selectedFilePaths = [];
          const selectedAppHistory = [];
          for (let i = 0; i < treeView.selection.selectedIndexes.length; i++) {
            let index = treeView.selection.selectedIndexes[i];
            if (appHistoryViewModel.treeModel.hasChildren(index)) {
              continue;
            }
            const appHistory = appHistoryViewModel.treeModel.getChildrenHistory(index);
            selectedFilePaths.push(appHistory[0].path);
            selectedAppHistory.push(appHistory[0])
          }
          if(selectedFilePaths.length == 0) return;
          root.selectedFilePaths_ = selectedFilePaths
          root.selectedAppHistory_ = selectedAppHistory
          tableMenu.popup(parent, x, y)
        }

        onExpanded: {
          if(!treeView.expandedMap) {
            treeView.expandedMap = {}
          }
          treeView.expandedMap[index.row] = true
        }

        onCollapsed: {
          if(!treeView.expandedMap) {
            treeView.expandedMap = {}
          }
          treeView.expandedMap[index.row] = false
        }

        onDbClickedRow: {
          skyFileMenu.openFiles([filepath])
        }

        QC1.TableViewColumn {
          role: "index"
          title: qsTr("序号")
          width: 67
          horizontalAlignment: Qt.AlignLeft | Qt.AlignVCenter
          delegate: Item {
            width: 67
            height: parent.height
            SkyText {
              anchors.fill: parent
              font.pixelSize: skyTheme.fontSize - 1
              clip: true
              visible: !styleData.hasChildren
              horizontalAlignment: Text.AlignHCenter
              verticalAlignment: Text.AlignVCenter
              text: styleData.index.row + 1
              elide: Text.ElideRight
              textFormat: Text.StyledText
              color: skyTheme.mainFontColor
              wrapMode: Text.NoWrap
            }
          }
        }
        QC1.TableViewColumn {
          role: "icon"
          title: qsTr("")
          width: 30
          movable: false
          horizontalAlignment: Qt.AlignRight | Qt.AlignVCenter
        }
        QC1.TableViewColumn {
          role: "fileNameHighlighted"
          title: qsTr("名称")
          width: 300
        }
        QC1.TableViewColumn {
          role: "size"
          title: qsTr("大小")
          width: 150
        }
        QC1.TableViewColumn {
          role: "extension"
          title: qsTr("扩展名")
          width: 150
        }
        QC1.TableViewColumn {
          role: "dirHighlighted"
          title: qsTr("所在目录")
          width: 200
        }
        QC1.TableViewColumn {
          role: "exists"
          title: qsTr("是否存在")
          width: 200
        }
        QC1.TableViewColumn {
          role: "lastModificationTime"
          title: qsTr("最后修改时间")
          width: 200
        }
        QC1.TableViewColumn {
          role: "user"
          title: qsTr("用户")
          width: 200
        }
        QC1.TableViewColumn {
          role: "source"
          title: qsTr("来源")
          width: 200
        }
        // QC1.TableViewColumn {
        //   role: "operator"
        //   title: qsTr("操作")
        //   width: 200
        //   horizontalAlignment: Qt.AlignHCenter | Qt.AlignVCenter
        //   delegate: RowLayout {
        //     anchors.fill: parent

        //     Component.onCompleted: {
        //       // 默认展开所有节点
        //       if(styleData.hasChildren && !styleData.isExpanded) {
        //         expandTree({}, true);
        //       }
        //     }
            
        //     SkyButton {
        //       Layout.preferredWidth: 70
        //       Layout.preferredHeight: 20
        //       danger: true
        //       fontSize: skyTheme.fontSizeSmall
        //       text: qsTr("删除文件")
        //       type: "text"
        //       size: "small"
        //       iconSource: SkyIcons.Delete
        //       visible: !styleData.hasChildren
        //       disabled: !(model && model.exists == "存在" && viewModel.license.level == QLicense.Ultimate)
        //       onClicked: {
        //         if (!disabled) {
        //           skyFileMenu.cleanFiles([model.path])
        //         }
        //       }
        //     }
            
        //     SkyButton {
        //       Layout.preferredWidth: 70
        //       Layout.preferredHeight: 20
        //       danger: true
        //       fontSize: skyTheme.fontSizeSmall
        //       text: qsTr("删除痕迹")
        //       type: "text"
        //       iconSource: SkyIcons.Delete
        //       size: "small"
        //       // 最近访问记录不允许全部删除
        //       visible: !(model && model.appType == "Recent" && styleData.hasChildren)
        //       disabled: viewModel.license.level !== QLicense.Ultimate
        //       onClicked: {
        //         if(appHistoryViewModel.treeModel.hasChildren(styleData.index)) {
        //           const appHistory = appHistoryViewModel.treeModel.getChildrenHistory(styleData.index);
        //           skyFileMenu.cleanAppHistory(appHistory)
        //         } else {
        //           skyFileMenu.cleanAppHistory([
        //             { "path": model.path, "appType": model.appType, "appName": model.appName }
        //           ])
        //         }
        //       }
        //     }
        //   }
        // }
      }
    }

    // 空数据
    Item {
      Layout.fillWidth: true
      Layout.fillHeight: true
      SkyEmpty {
        anchors.fill: parent
        text: {
          if (appHistoryViewModel.indexState == MainWindowViewModel.Init) {
            return qsTr("准备加载")
          }

          if (appHistoryViewModel.treeModel && appHistoryViewModel.treeModel.size == 0)
            return qsTr("没有搜索结果<br><br><font color=\"#666\" size=\"1\">本机可能没有符合搜索条件的文件痕迹</font>")

          return ""
        }
        size: 220
        isLoading: {
          return appHistoryViewModel.indexState == MainWindowViewModel.Loading || appHistoryViewModel.searching
        }
      }
    }
  }
  
  // TODO)) 暂时无法通过下标获取到 index 后续支持
  // 右键菜单遮罩，用来阻止table表格滚动事件
  Rectangle {
    anchors.fill: parent
    visible: tableMenu.visible
    color: Qt.rgba(0, 0, 0, 0)
    MouseArea {
      anchors.fill: parent
      onWheel: { }
      onPressed: mouse.accepted = false;
      onReleased: mouse.accepted = false;
      onClicked: mouse.accepted = false;
    }
  }
  // 右键菜单
  SkyMenu {
    id: tableMenu
    focus: true
    SkyMenuItem {
      text: qsTr("打开")
      visible: {
        return selectedFilePaths_.length == 1
      }
      iconSource: SkyIcons.OpenFile
      onTriggered: {
        skyFileMenu.openFiles(selectedFilePaths_)
      }
    }
    SkyMenuItem {
      text: qsTr("打开所在目录")
      iconSource: SkyIcons.OpenLocal
      visible: {
        return selectedFilePaths_.length == 1
      }
      onTriggered: {
        skyFileMenu.openFolder(selectedFilePaths_[0])
      }
    }
    SkyMenuItem {
      text: qsTr("复制名称")
      iconSource: SkyIcons.Set
      onTriggered: {
        const isSuccess = skyFileMenu.copyFileNames(selectedFilePaths_)
        if (isSuccess) {
          showSuccess(qsTr("复制名称成功"))
        }
      }
    }
    SkyMenuItem {
      text: qsTr("复制路径")
      iconSource: SkyIcons.DuplexPortraitOneSided
      onTriggered: {
        const isSuccess = skyFileMenu.copyFilePaths(selectedFilePaths_)
        if (isSuccess) {
          showSuccess(qsTr("复制路径成功"))
        }
      }
    }
    SkyMenuItem {
      text: {
        if (disabled) {
          return qsTr("深度文件清理（高级版）")
        }
        return qsTr("深度文件清理")
      }
      iconSource: SkyIcons.ClearAllInkMirrored
      disabled: {
        return viewModel.license.level !== QLicense.Ultimate && !viewModel.skipLicense
      }
      onTriggered: {
        if (disabled) {
          showWarning(qsTr("仅高级版支持该功能"))
          return
        }
        skyFileMenu.cleanFiles(selectedFilePaths_)
      }
    }
    SkyMenuItem {
      text: {
        if (disabled) {
          return qsTr("痕迹清理（高级版）")
        }
        return qsTr("痕迹清理")
      }
      visible: {
        // if (selectedAppHistory_.length > 0){
        //   return selectedAppHistory_[0].appType == "Windows 管理器" || selectedAppHistory_[0].appType == "Office 管理器"
        // }
        return true
      }
      iconSource: SkyIcons.ClearAllInkMirrored
      disabled: {
        return viewModel.license.level !== QLicense.Ultimate && !viewModel.skipLicense
      }
      onTriggered: {
        if (disabled) {
          showWarning(qsTr("仅高级版支持该功能"))
          return
        }
        skyFileMenu.cleanAppHistory(selectedAppHistory_)
      }
    }
  }

  Timer {
    id: timer
  }
  function delay(delayTime,cb) {
    timer.interval = delayTime;
    timer.repeat = false;
    timer.triggered.connect(cb);
    timer.start();
  }

  /**
   * @brief 展开指定的树
   * @param {object} expandedMap 用来指示哪些树需要展开 当为 undefined 时，表示全部展开
   */
  function expandTree(expandedMap, expandAll = false) {
    for(let i = 0; i < appHistoryViewModel.treeModel.size; i++) {
      const index = appHistoryViewModel.treeModel.index(i, 0);
      if (expandAll || (expandedMap && expandedMap[index.row])) {
        if(!treeView.isExpanded(index)) {
          treeView.expand(index)
        }
      }
    }
  }
}