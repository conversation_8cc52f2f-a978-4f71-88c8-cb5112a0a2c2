// TODO)) 和QC1.TableViewColumn 一一对应
let COLUMN_INFO = [
  {
    name: "index",
    sortName: "index",
    title: "序号",
    sortable: false,
    sortOrder: null,
    enableBorder: true,
  },
  {
    name: "icon",
    sortName: "icon",
    title: "",
    sortable: false,
    sortOrder: null,
    isImage: true,
    hAlign: "left",
    enableBorder: false,
  },
  {
    name: "fileNameHighlighted",
    sortName: "fileName",
    title: "名称",
    sortable: true,
    sortOrder: null,
    enableTooltip: false,
    enableBorder: true,
  },
  {
    name: "size",
    sortName: "size",
    title: "大小",
    sortable: true,
    sortOrder: null,
    hAlign: "center",
    enableBorder: true,
  },
  {
    name: "extension",
    sortName: "extension",
    title: "扩展名",
    sortable: true,
    sortOrder: null,
    hAlign: "center",
    enableBorder: true,
  },
  {
    name: "dirHighlighted",
    sortName: "dir",
    title: "所在目录",
    sortable: true,
    sortOrder: null,
    enableTooltip: true,
    enableBorder: true,
  },
  {
    name: "exists",
    sortName: "exists",
    title: "是否存在",
    sortable: true,
    sortOrder: null,
    hAlign: "center",
    enableTooltip: false,
    enableBorder: true,
  },
  {
    name: "lastModificationTime",
    sortName: "lastModificationTime",
    title: "最后修改时间",
    sortable: true,
    sortOrder: null,
    hAlign: "center",
    enableTooltip: false,
    enableBorder: true,
  },
  {
    name: "user",
    sortName: "user",
    title: "用户",
    sortable: true,
    sortOrder: null,
    hAlign: "center",
    enableTooltip: false,
    enableBorder: true,
  },
  {
    name: "source",
    sortName: "source",
    title: "来源",
    sortable: true,
    sortOrder: null,
    hAlign: "center",
    enableTooltip: false,
    enableBorder: true,
  },
  // {
  //   name: "operator",
  //   sortName: "operator",
  //   title: "操作",
  //   sortable: false,
  //   sortOrder: null,
  //   hAlign: "center",
  //   enableTooltip: false,
  //   enableBorder: false,
  // },
  // {
  //   name: "lastAccessTime",
  //   sortName: "lastAccessTime",
  //   title: "最后访问时间",
  //   sortable: true,
  //   sortOrder: null,
  //   hAlign: "center"
  // },
];

function getColumnInfo(columnIndex) {
  return COLUMN_INFO[columnIndex];
}

function getColumnIndexByName(name) {
  return COLUMN_INFO.findIndex((info) => info.name === name);
}
