import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Window 2.15
import QtQuick.Controls.Universal 2.12
import QtQuick.Layouts 1.15
import AnywhereQmlModule 1.0
import SkyUi 1.0
import QtQuick.Controls 1.2 as QC1
import QtQuick.Dialogs 1.1
import "./CleanFileUtils.js" as Utils

ApplicationWindow {
  id: root
  width: 500
  height: 320
  minimumWidth: 500
  minimumHeight: 320
  maximumHeight: 320
  title: {
    if (model.state == CleanAppHistoryWindowViewModel.Running)
      return qsTr("正在清除文件...")
    if (model.state == CleanAppHistoryWindowViewModel.Success)
      return qsTr("清除文件成功")
    if (model.state == CleanAppHistoryWindowViewModel.Error)
      return qsTr("清除文件失败")

    return ""
  }
  visible: true
  flags: Qt.Dialog
  modality: Qt.WindowModal
  color: "#00000000"
  
  property bool requestClose: false
  property bool expand: false
  
  background: Rectangle {
    id: rootBackground
    anchors.fill: parent
    color: "#f5f5f5"
    border.width: 1
    border.color: "#A0A0A0"
  }

  CleanAppHistoryWindowViewModel {
    id: model

    onStateChanged: {
      if ((model.state == CleanAppHistoryWindowViewModel.Success || model.state == CleanAppHistoryWindowViewModel.Error) && root.requestClose)
      {
        root.close()
      }
    }
  }

  ColumnLayout {
    anchors.fill: parent
    anchors.margins: 10
    Item {
      id: collapseWrapper
      Layout.fillWidth: true
      Layout.fillHeight: true
      
      SkyCollapse {
        id: cleanFileCollapse
        width: parent.width
        expand: true
        enabledExpand: false
        iconSource: getIconSource(model.state, model.errorCount, model.done)
        iconColor: getIconColor(model.state, model.errorCount, model.done)
        
        headerText: {
          if (model.state == CleanAppHistoryWindowViewModel.Success || model.state == CleanAppHistoryWindowViewModel.Error) {
            if (!model.errorCount && !model.done) {
              return qsTr("记录已经被清理")
            }
            return qsTr("清理" + model.done + "个文件的痕迹，成功" + model.successCount + "条，失败" + model.errorCount + "条")
          }
          return qsTr("正在清理" + model.done + "个文件的痕迹，成功" + model.successCount + "条，失败" + model.errorCount + "条")
        }
        descText: qsTr("清理文件痕迹")
        progressValue: (model.successCount + model.errorCount) / model.done
        contentHeight: 200

        Rectangle {
          anchors.fill: parent
          color: "#fcfcfc"
          Item {
            width: parent.width - 20
            height: parent.height
            anchors {
              left: parent.left
              leftMargin: 10
            }
            
            SkyList {
              anchors.fill: parent
              model: model.resultListModel
              delegate: Rectangle {
                width: parent.width
                height: childrenRect.height + 10
                color: "#fcfcfc"
                Item {
                  width: parent.width - 20
                  height: childrenRect.height
                  anchors {
                    left: parent.left
                    leftMargin: 10
                    top: parent.top
                    topMargin: 5
                    bottomMargin: 5
                  }
                  SkyTextEdit {
                    height: contentHeight
                    width: parent.width
                    verticalAlignment: Text.AlignVCenter
                    color: skyTheme.minorFontColor
                    font.pixelSize: skyTheme.fontSize - 1
                    wrapMode: TextEdit.WrapAnywhere
                    anchors {
                      top: parent.top
                      topMargin: 5
                    }
                    text: {
                      let result = name + "已清理" + done + "条记录，成功" + successCount + "条";

                      if (errorCount) {
                        result += "，失败" + errorCount + "条；失败原因：" + message;
                      }
                      return qsTr(result);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    
    Item {
      Layout.fillWidth: true
      Layout.preferredHeight: 36
      RowLayout {
        anchors.fill: parent
        // layoutDirection: Qt.RightToLeft
        Item {
          Layout.fillWidth: true
          Layout.fillHeight: true
        }

        Item {
          Layout.preferredWidth: 60
          Layout.fillHeight: true
          SkyButton {
            type: "text"
            text: {
              if (model.state == CleanAppHistoryWindowViewModel.Running)
                return qsTr("中止")
              return qsTr("关闭")
            }
            height: 32
            width: parent.width
            onClicked: {
              root.close()
            }
          }
        }
      }
    }
  }

  function start(fileNames, isCloseApp)
  {
    model.start(fileNames, isCloseApp)
  }

  SkyMessageDialog {
    id: messageDialog
    title: qsTr("中止操作")
    text: qsTr("中止本次清除文件操作?")
    isCancel: true
    isAccept: true
    onAccepted: {
      root.requestClose = true
      model.cancel()
    }
  }

  onClosing: {
    closeShared()
    if (model.state == CleanAppHistoryWindowViewModel.Running)
    {
      messageDialog.open()
      close.accepted = false
    }
  }

  function getIconSource(modelState, errorCount, done) {
    if (modelState == CleanAppHistoryWindowViewModel.Success || modelState == CleanAppHistoryWindowViewModel.Error) {
      if (errorCount == done && done > 0) {
        // 全部失败
        return SkyIcons.StatusErrorFull
      }
      if (errorCount > 0) {
        // 部分失败
        return SkyIcons.InfoSolid
      }

      if (errorCount == 0 && done == 0) {
        // 痕迹已经被删除 (文件内容清理时删除该文件)
        return SkyIcons.InfoSolid
      }

      // 成功
      return SkyIcons.CompletedSolid
    }
    return SkyIcons.Refresh
  }

  function getIconColor(modelState, errorCount, done) {
    if (modelState == CleanAppHistoryWindowViewModel.Success || modelState == CleanAppHistoryWindowViewModel.Error) {
      if (errorCount == done && done > 0) {
        // 全部失败
        return skyTheme.dangerBackground
      }
      if (errorCount > 0) {
        // 部分失败
        return skyTheme.warningBackground
      }

      if (errorCount == 0 && done == 0) {
        // 痕迹已经被删除 (文件内容清理时删除该文件)
        return skyTheme.warningBackground
      }

      // 成功
      return skyTheme.successBackground
    }
    return "#fff"
  }

  Component.onCompleted: {
    if (Qt.platform.os === "linux") {
      root.x = (Screen.width - root.width) / 2
      root.y = (Screen.height - root.height) / 2
    }
  }
}