// TODO)) 和QC1.TableViewColumn 一一对应
let COLUMN_INFO = [
  {
    name: "name",
    sortName: "name",
    title: "名称",
    sortable: true,
    sortOrder: null,
    enableTooltip: false,
    enableBorder: true,
  },
  {
    name: "successStr",
    sortName: "successStr",
    title: "处理结果",
    sortable: true,
    sortOrder: null,
    enableTooltip: false,
    enableBorder: true,
  },
  {
    name: "message",
    sortName: "message",
    title: "备注",
    sortable: true,
    sortOrder: null,
    enableTooltip: false,
    enableBorder: true,
  }
];

function getColumnInfo(columnIndex) {
  return COLUMN_INFO[columnIndex];
}

function getColumnIndexByName(name) {
  return COLUMN_INFO.findIndex((info) => info.name === name);
}
