import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Layouts 1.15
import SkyUi 1.0
import AnywhereQmlModule 1.0

SkyCard {
  property string name: ""
  property string fileNameQuery: ""

  // 高级筛选属性
  property string modifyTimeFrom: ""
  property string modifyTimeTo: ""
  property string fileSizeMin: ""
  property string fileSizeMax: ""
  property bool showAdvancedFilters: false

  anchors.fill: parent
  signal formChage(string fileName, string modifyTimeFrom, string modifyTimeTo,
                   string fileSizeMin, string fileSizeMax)

  backgroundValue: "transparent"
  paddingValue: 0
  marginValue: 0
  radiusValue: 0
  id: root

  // 日期选择器组件
  property string currentDateField: ""
  property var currentDatePicker: null

  Component {
    id: datePickerComponent
    Item {
      property string dateValue: ""
      property string placeholderText: "yyyy-MM-dd"
      property bool isOpen: false
      signal dateChanged(string newDate)

      id: datePickerRoot
      width: 100
      height: 25

      // 主输入框
      Rectangle {
        anchors.fill: parent
        border.color: datePickerRoot.isOpen ? "#1890FF" : "#d9d9d9"
        border.width: 1
        radius: 2
        color: "#ffffff"

        // 主输入区域
        Row {
          anchors.fill: parent
          anchors.margins: 1
          spacing: 0

          // 日期显示区域
          Rectangle {
            width: parent.width - 18
            height: parent.height
            color: "transparent"

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: datePickerRoot.dateValue || datePickerRoot.placeholderText
              color: datePickerRoot.dateValue ? "#333" : "#999"
              font.pixelSize: skyTheme.fontSize || 11
            }

            MouseArea {
              anchors.fill: parent
              onClicked: {
                datePickerRoot.isOpen = !datePickerRoot.isOpen
              }
            }
          }

          // 日历图标
          Rectangle {
            width: 18
            height: parent.height
            color: "transparent"

            Text {
              anchors.centerIn: parent
              text: "📅"
              font.pixelSize: 10
              color: "#666"
            }

            MouseArea {
              anchors.fill: parent
              onClicked: {
                datePickerRoot.isOpen = !datePickerRoot.isOpen
              }
            }
          }
        }
      }

      // 日历面板（简化版，只保留核心功能）
      Rectangle {
        id: calendarPanel
        visible: datePickerRoot.isOpen
        width: 280
        height: 320
        y: parent.height + 2
        x: Math.max(0, Math.min(parent.width - width, 0))
        color: "#ffffff"
        border.color: "#d9d9d9"
        border.width: 1
        radius: 4
        z: 1000

        // 阻止点击事件透传到下面的表格
        MouseArea {
          anchors.fill: parent
          onClicked: {
            // 阻止事件透传，不做任何操作
          }
        }

        property int currentYear: new Date().getFullYear()
        property int currentMonth: new Date().getMonth() + 1

        Column {
          anchors.fill: parent
          anchors.margins: 10
          spacing: 8

          // 年月导航
          Row {
            width: parent.width
            height: 30

            Rectangle {
              width: 30
              height: 30
              color: "transparent"

              Text {
                anchors.centerIn: parent
                text: "◀"
                font.pixelSize: 14
                color: "#666"
              }

              MouseArea {
                anchors.fill: parent
                onClicked: {
                  if (calendarPanel.currentMonth === 1) {
                    calendarPanel.currentMonth = 12
                    calendarPanel.currentYear--
                  } else {
                    calendarPanel.currentMonth--
                  }
                }
              }
            }

            Item {
              width: parent.width - 60
              height: 30

              Text {
                anchors.centerIn: parent
                text: calendarPanel.currentYear + "年" + calendarPanel.currentMonth + "月"
                font.pixelSize: 14
                font.bold: true
                color: "#333"
              }
            }

            Rectangle {
              width: 30
              height: 30
              color: "transparent"

              Text {
                anchors.centerIn: parent
                text: "▶"
                font.pixelSize: 14
                color: "#666"
              }

              MouseArea {
                anchors.fill: parent
                onClicked: {
                  if (calendarPanel.currentMonth === 12) {
                    calendarPanel.currentMonth = 1
                    calendarPanel.currentYear++
                  } else {
                    calendarPanel.currentMonth++
                  }
                }
              }
            }
          }

          // 星期标题
          Row {
            width: parent.width
            height: 25

            Repeater {
              model: ["日", "一", "二", "三", "四", "五", "六"]
              Rectangle {
                width: (parent.width) / 7
                height: 25
                color: "#f5f5f5"

                Text {
                  anchors.centerIn: parent
                  text: modelData
                  font.pixelSize: skyTheme.fontSize || 11
                  color: "#666"
                  font.bold: true
                }
              }
            }
          }

          // 日期网格
          Grid {
            width: parent.width
            height: 180
            columns: 7
            rows: 6

            Repeater {
              model: 42 // 6周 * 7天

              delegate: Rectangle {
                width: parent.width / 7
                height: 30

                property int dayNumber: {
                  var firstDay = new Date(calendarPanel.currentYear, calendarPanel.currentMonth - 1, 1)
                  var startDay = firstDay.getDay() // 0=Sunday, 1=Monday, etc.
                  return index - startDay + 1
                }

                property bool isValidDay: {
                  var daysInMonth = new Date(calendarPanel.currentYear, calendarPanel.currentMonth, 0).getDate()
                  return dayNumber >= 1 && dayNumber <= daysInMonth
                }

                property bool isToday: {
                  if (!isValidDay) return false
                  var today = new Date()
                  return dayNumber === today.getDate() &&
                         calendarPanel.currentMonth === (today.getMonth() + 1) &&
                         calendarPanel.currentYear === today.getFullYear()
                }

                property bool isSelected: {
                  if (!datePickerRoot.dateValue || !isValidDay) return false
                  try {
                    var parts = datePickerRoot.dateValue.split("-")
                    if (parts.length !== 3) return false
                    var year = parseInt(parts[0])
                    var month = parseInt(parts[1])
                    var day = parseInt(parts[2])
                    if (isNaN(year) || isNaN(month) || isNaN(day)) return false
                    return year === calendarPanel.currentYear &&
                           month === calendarPanel.currentMonth &&
                           day === dayNumber
                  } catch (e) {
                    console.warn("日期解析错误:", e)
                    return false
                  }
                }

                color: {
                  if (!isValidDay) return "transparent"
                  if (isToday) return "#1890FF"
                  if (isSelected) return "#bae7ff"
                  if (dayMouseArea.containsMouse) return "#e6f7ff"
                  return "transparent"
                }

                border.color: isToday ? "#ffffff" : "transparent"
                border.width: isToday ? 1 : 0
                radius: 2

                Text {
                  anchors.centerIn: parent
                  text: parent.isValidDay ? parent.dayNumber : ""
                  font.pixelSize: skyTheme.fontSize || 11
                  color: {
                    if (!parent.isValidDay) return "transparent"
                    if (parent.isToday) return "white"
                    if (parent.isSelected) return "#1890FF"
                    return "#333"
                  }
                  font.bold: parent.isToday || parent.isSelected
                }

                MouseArea {
                  id: dayMouseArea
                  anchors.fill: parent
                  hoverEnabled: true
                  enabled: parent.isValidDay
                  cursorShape: enabled ? Qt.PointingHandCursor : Qt.ArrowCursor

                  onClicked: {
                    try {
                      var month = calendarPanel.currentMonth < 10 ? "0" + calendarPanel.currentMonth : calendarPanel.currentMonth.toString()
                      var day = parent.dayNumber < 10 ? "0" + parent.dayNumber : parent.dayNumber.toString()
                      var dateStr = calendarPanel.currentYear + "-" + month + "-" + day
                      datePickerRoot.dateValue = dateStr
                      datePickerRoot.dateChanged(dateStr)
                      datePickerRoot.isOpen = false
                    } catch (e) {
                      console.warn("日期点击处理错误:", e)
                    }
                  }
                }
              }
            }
          }

          // 底部按钮
          Row {
            anchors.horizontalCenter: parent.horizontalCenter
            spacing: 10

            Rectangle {
              width: 60
              height: 25
              color: "#52c41a"
              radius: 2

              Text {
                anchors.centerIn: parent
                text: "今天"
                color: "white"
                font.pixelSize: skyTheme.fontSize || 11
              }

              MouseArea {
                anchors.fill: parent
                onClicked: {
                  try {
                    var today = new Date()
                    var year = today.getFullYear()
                    var month = today.getMonth() + 1
                    var day = today.getDate()
                    var dateStr = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day)

                    calendarPanel.currentYear = year
                    calendarPanel.currentMonth = month
                    datePickerRoot.dateValue = dateStr
                    datePickerRoot.dateChanged(dateStr)
                    datePickerRoot.isOpen = false
                  } catch (e) {
                    console.warn("今天按钮处理错误:", e)
                  }
                }
              }
            }

            Rectangle {
              width: 60
              height: 25
              color: "#ff4d4f"
              radius: 2

              Text {
                anchors.centerIn: parent
                text: "清空"
                color: "white"
                font.pixelSize: skyTheme.fontSize || 11
              }

              MouseArea {
                anchors.fill: parent
                onClicked: {
                  datePickerRoot.dateValue = ""
                  datePickerRoot.dateChanged("")
                  datePickerRoot.isOpen = false
                }
              }
            }

            Rectangle {
              width: 60
              height: 25
              color: "#1890FF"
              radius: 2

              Text {
                anchors.centerIn: parent
                text: "取消"
                color: "white"
                font.pixelSize: skyTheme.fontSize || 11
              }

              MouseArea {
                anchors.fill: parent
                onClicked: {
                  datePickerRoot.isOpen = false
                }
              }
            }
          }
        }
      }
    }
  }

  onFileNameQueryChanged: {
    if (fileName.text == fileNameQuery) return;
    fileName.text = fileNameQuery;
  }

  content: Item {
    anchors.fill: parent

    // 文件名搜索行
    RowLayout {
      id: fileNameRow
      x: {
        return 0
      }
      y: {
        return 0
      }
      width: parent.width - searchButton.width - advancedFilterButton.width - 50  // 20(高级按钮左边距) + 15(按钮间距) + 15(搜索按钮右边距)
      height: parent.height

      SkyTitle {
        text: qsTr("文件名称: ")
        Layout.preferredWidth: 80  // 使用固定宽度，与修改时间保持一致
        Layout.alignment: Qt.AlignVCenter | Qt.AlignRight
        Layout.fillHeight: true
        description: qsTr("仅搜索\"名称\"和\"所在目录\"")
      }

      SkyInput {
        id: fileName
        Layout.fillWidth: true
        implicitHeight: parent.height
        placeholderText: qsTr("在此输入文件名搜索条件")

        onTextChanged: {
          if (root.fileNameQuery == text) return;
          root.fileNameQuery = text;
        }

        Keys.onEnterPressed: {
          root.formChage(fileName.text, root.modifyTimeFrom, root.modifyTimeTo,
                        root.fileSizeMin, root.fileSizeMax)
        }

        Keys.onReturnPressed: {
          root.formChage(fileName.text, root.modifyTimeFrom, root.modifyTimeTo,
                        root.fileSizeMin, root.fileSizeMax)
        }
      }
    }

    // 高级筛选展开按钮
    SkyButton {
      id: advancedFilterButton
      width: 70  // 增加宽度
      x: 5 + fileNameRow.width + 20  // 5是fileNameRow的x位置
      y: 0
      height: parent.height
      type: "text"
      text: "高级"
      iconSource: root.showAdvancedFilters ? SkyIcons.Up : SkyIcons.Down
      skyIconColor: "#1890FF"  // 蓝色箭头
      fontColor: "#1890FF"     // 蓝色文字
      contentDescription: root.showAdvancedFilters ? "收起高级筛选" : "展开高级筛选"
      onClicked: {
        root.showAdvancedFilters = !root.showAdvancedFilters
      }
    }

    // 搜索按钮
    SkyButton {
      id: searchButton
      width: 62
      x: 5 + fileNameRow.width + 20 + advancedFilterButton.width + 15  // 5是fileNameRow的x位置
      y: 0
      height: parent.height
      type: "primary"
      text: qsTr("搜索")
      onClicked: {
        root.formChage(fileName.text, root.modifyTimeFrom, root.modifyTimeTo,
                      root.fileSizeMin, root.fileSizeMax)
        forceActiveFocus()
      }
    }

    // 高级筛选区域
    Rectangle {
      id: advancedFiltersArea
      visible: root.showAdvancedFilters
      width: parent.width
      height: visible ? 50 : 0
      y: parent.height + 10  // AppHistoryComponents 没有 previewerEnabled，直接使用 parent.height
      color: "#f8f9fa"  // 浅灰色背景
      border.color: "#1890ff"  // 蓝色边框
      border.width: 1
      radius: 6  // 圆角
      // 单行布局：修改时间、文件大小
      RowLayout {
        anchors.fill: parent
        anchors.leftMargin: 15    // 增加左边距，与边框保持距离
        anchors.topMargin: 10
        anchors.rightMargin: 10
        anchors.bottomMargin: 10
        spacing: 15

        // 修改时间区域
        RowLayout {
          spacing: 5

          SkyTitle {
            text: qsTr("修改时间:")
            Layout.preferredWidth: Math.max(60, implicitWidth + 10)
            font.pixelSize: skyTheme.fontSize || 11
            description: qsTr("筛选指定修改时间范围内的文件")
          }

          Loader {
            id: modifyTimeFromLoader
            sourceComponent: datePickerComponent
            Layout.preferredWidth: 100
            Layout.preferredHeight: 30
            onLoaded: {
              if (item) {
                try {
                  item.dateValue = Qt.binding(function() { return root.modifyTimeFrom || "" })
                  item.placeholderText = "开始日期"
                  item.dateChanged.connect(function(newDate) {
                    root.modifyTimeFrom = newDate || ""
                  })
                } catch (e) {
                  console.warn("修改时间From加载错误:", e)
                }
              }
            }
          }

          SkyTitle {
            text: qsTr("至")
            Layout.preferredWidth: 15
            font.pixelSize: skyTheme.fontSize || 11
          }

          Loader {
            id: modifyTimeToLoader
            sourceComponent: datePickerComponent
            Layout.preferredWidth: 100
            Layout.preferredHeight: 30
            onLoaded: {
              if (item) {
                try {
                  item.dateValue = Qt.binding(function() { return root.modifyTimeTo || "" })
                  item.placeholderText = "结束日期"
                  item.dateChanged.connect(function(newDate) {
                    root.modifyTimeTo = newDate || ""
                  })
                } catch (e) {
                  console.warn("修改时间To加载错误:", e)
                }
              }
            }
          }
        }

        // 分隔线
        Rectangle {
          Layout.preferredWidth: 1
          Layout.fillHeight: true
          color: "#e9ecef"
        }

        // 文件大小区域
        RowLayout {
          spacing: 5

          SkyTitle {
            text: qsTr("文件大小:")
            Layout.preferredWidth: Math.max(60, implicitWidth + 10)
            font.pixelSize: skyTheme.fontSize || 11
            description: qsTr("筛选指定大小范围内的文件\n支持单位：B、KB、MB、GB、TB\n例如：1024、1.5MB、2GB")
          }

          Rectangle {
            Layout.preferredWidth: 120  // 增加宽度从80到120
            Layout.preferredHeight: 30  // 与主搜索框高度一致
            border.color: "#ccc"
            border.width: 1
            radius: 2

            TextInput {
              id: fileSizeMinInput
              anchors.fill: parent
              anchors.margins: 5
              verticalAlignment: TextInput.AlignVCenter
              font.pixelSize: skyTheme.fontSize || 11
              text: root.fileSizeMin
              onTextChanged: {
                root.fileSizeMin = text
              }

              inputMethodHints: Qt.ImhNone
              selectByMouse: true
            }

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: "最小值(如1MB)"
              color: "#999"
              font.pixelSize: skyTheme.fontSize || 10
              visible: fileSizeMinInput.text.length === 0
            }
          }

          SkyTitle {
            text: qsTr("至")
            Layout.preferredWidth: 15
            font.pixelSize: skyTheme.fontSize || 11
          }

          Rectangle {
            Layout.preferredWidth: 120  // 增加宽度从80到120
            Layout.preferredHeight: 30  // 与主搜索框高度一致
            border.color: "#ccc"
            border.width: 1
            radius: 2

            TextInput {
              id: fileSizeMaxInput
              anchors.fill: parent
              anchors.margins: 5
              verticalAlignment: TextInput.AlignVCenter
              font.pixelSize: skyTheme.fontSize || 11
              text: root.fileSizeMax
              onTextChanged: {
                root.fileSizeMax = text
              }

              inputMethodHints: Qt.ImhNone
              selectByMouse: true
            }

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: "最大值(如10GB)"
              color: "#999"
              font.pixelSize: skyTheme.fontSize || 10
              visible: fileSizeMaxInput.text.length === 0
            }
          }
        }

        // 填充剩余空间
        Item { Layout.fillWidth: true }
      }
    }
  }
}