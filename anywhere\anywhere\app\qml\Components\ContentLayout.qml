import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Controls.Universal 2.12
import QtQuick.Layouts 1.15
import "../FileContentComponents" as FileContentComponents
import "../AppHistoryComponents" as AppHistoryComponents
import "../DeletedFileComponents" as DeletedFileComponents
import AnywhereQmlModule 1.0
import QtQuick.Controls 1.2 as QC1
import QtGraphicalEffects 1.15
import SkyUi 1.0

Item {
  id: contentRoot
  anchors.fill: parent

  signal close()

  ColumnLayout {
    spacing: 0
    anchors.fill: parent

    // 顶部导航栏
    Rectangle {
      id: menu
      property int activeId: 0
      Layout.fillWidth: true
      Layout.preferredHeight: 40
      color: "#F5F5F5"
      Layout.leftMargin: 1
      Layout.rightMargin: 1
      SkyRectangle {
        id: fileContentTab
        actived: menu.activeId == 0
        width: 180
        height: parent.height
        color: actived ? "#1890FF": "#F5F5F5"
        cornersRadius: [0, 10, 0, 0]
        z: actived ? 2: 0
        hoverColor: skyTheme.hoverPrimaryColor
        onClicked: {
          menu.activeId = 0
        }
        SkyText {
          text: qsTr("文件内容检查")
          font.weight: Font.Bold
          anchors.centerIn: parent
          color: fileContentTab.actived ? "#fff" : "#000"
        }
      }
      SkyRectangle {
        id: fileTraceTab
        width: 180
        x: 180
        actived: menu.activeId == 1
        height: parent.height
        color: actived ? "#1890FF": "#F5F5F5"
        cornersRadius: [10, 10, 0, 0]
        z: actived ? 2: 0
        hoverColor: skyTheme.hoverPrimaryColor
        onClicked: {
          appHistoryLoader.active = true
          menu.activeId = 1
        }
        SkyText {
          text: qsTr("文件痕迹检查")
          font.weight: Font.Bold
          anchors.centerIn: parent
          color: fileTraceTab.actived ? "#fff" : "#000"
        }
      }
      SkyRectangle {
        id: fileDeletedcTab
        actived: menu.activeId == 2
        width: 180
        x: 360
        height: parent.height
        color: actived ? "#1890FF": "#F5F5F5"
        cornersRadius: [0, 10, 0, 0]
        z: actived ? 2: 0
        hoverColor: skyTheme.hoverPrimaryColor
        onClicked: {
          menu.activeId = 2
        }
        SkyText {
          text: qsTr("已删除文件检查")
          font.weight: Font.Bold
          anchors.centerIn: parent
          color: fileDeletedcTab.actived ? "#fff" : "#000"
        }
      }

      Rectangle {
        height: 1
        width: parent.width
        y: parent.height - 1
        color: "#D1D1D1"
        anchors.leftMargin: 1
        anchors.rightMargin: 1
        z: 0
      }
    }

    // 内容区域
    Item {
      id: contentPage
      Layout.fillWidth: true
      Layout.fillHeight: true
      Layout.margins: 10
      Layout.topMargin: 0
      Layout.bottomMargin: 0
      StackLayout {
        anchors.fill: parent
        currentIndex: menu.activeId

        Loader {
          id: fileContentLoader
          // 只在需要时加载
          active: true
          sourceComponent: FileContentComponents.ContentLayout {
            id: fileContent
          }
        }

        Loader {
          id: appHistoryLoader
          // 只在需要时加载
          active: false
          sourceComponent: AppHistoryComponents.ContentLayout {
            id: appHistory
            Component.onCompleted: {
              appHistoryViewModel.start()
            }
          }
        }
        Loader {
          id: deletedFileContentLoader
          // 只在需要时加载
          active: true
          sourceComponent: DeletedFileComponents.ContentLayout {
            id: deletedFileContent
          }
        }
      }
    }
  }

  onClose: {
    appHistoryViewModel.close();
    viewModel.close();
  }
}