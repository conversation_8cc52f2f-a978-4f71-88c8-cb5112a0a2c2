import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Window 2.15
import QtQuick.Controls.Universal 2.12
import QtQuick.Layouts 1.15
import AnywhereQmlModule 1.0
import SkyUi 1.0
import QtQuick.Controls 1.2 as QC1
import QtQuick.Dialogs 1.1
import "./CleanUtils.js" as Utils

ApplicationWindow {
  id: root
  width: 500
  height: 230
  minimumWidth: 500
  minimumHeight: 230
  maximumHeight: 440
  title: {
    if (model.state == CleanFilesWindowViewModel.Running)
      return qsTr("正在清除文件...")
    if (model.state == CleanFilesWindowViewModel.Success)
      return qsTr("清除文件成功")
    if (model.state == CleanFilesWindowViewModel.Error)
      return qsTr("清除文件失败")

    return qsTr("")
  }
  visible: true
  flags: Qt.Dialog
  modality: Qt.WindowModal
  color: "#00000000"
  
  property bool requestClose: false
  property bool expand: false
  
  background: Rectangle {
    id: rootBackground
    anchors.fill: parent
    color: "#f5f5f5"
    border.width: 1
    border.color: "#A0A0A0"
  }

  CleanFilesWindowViewModel {
    id: model

    onStateChanged: {
      if ((model.state == CleanFilesWindowViewModel.Success || model.state == CleanFilesWindowViewModel.Error) && root.requestClose)
      {
        root.close()
      }
    }
  }

  ColumnLayout {
    anchors.fill: parent
    anchors.margins: 10
    Item {
      id: collapseWrapper
      Layout.fillWidth: true
      Layout.fillHeight: true
      property int index: -1
      onIndexChanged: {
        // 当传递 index == n 给 SkyCollapse 时, 我们的 expand 会在内部修改, 造成第二次点击不生效的问题
        cleanFileCollapse.expand = false;
        cleanAppHistoryCollapse.expand = false;
        switch(index) {
          case 0:
            cleanFileCollapse.expand = true;
            break
          case 1:
            cleanAppHistoryCollapse.expand = true;
            break
        }
      }
      
      SkyCollapse {
        id: cleanFileCollapse
        width: parent.width
        onClicked: {
          if (collapseWrapper.index == 0) collapseWrapper.index = -1
          else collapseWrapper.index = 0
          if(root.height < root.maximumHeight) {
            root.height = root.maximumHeight
          }
        }
        iconSource: getIconSource(model.state, model.errorCount, model.done)
        iconColor: getIconColor(model.state, model.errorCount, model.done)
        
        headerText: {
          if (model.state == CleanFilesWindowViewModel.Success || model.state == CleanFilesWindowViewModel.Error) {
            return qsTr("清理" + model.done + "个文件，成功" + model.successCount + "个，失败" + model.errorCount + "个")
          }
          return qsTr("正在清理" + model.done + "个文件，成功" + model.successCount + "个，失败" + model.errorCount + "个")
        }
        descText: qsTr("清理文件内容")
        progressValue: (model.successCount + model.errorCount) / model.done
        contentHeight: 200

        Rectangle {
          anchors.fill: parent
          color: "#fcfcfc"
          Item {
            width: parent.width - 20
            height: parent.height
            anchors {
              left: parent.left
              leftMargin: 10
            }
            SkyTable {
              id: tableView
              anchors.fill: parent
              tableModel: model.fileResultTableModel
              sortColumns: Utils.COLUMN_INFO
              QC1.TableViewColumn {
                role: "name"
                title: qsTr("名称")
                width: tableView.width * 0.4
              }
              QC1.TableViewColumn {
                role: "successStr"
                title: qsTr("处理结果")
                width: tableView.width * 0.2
              }
              QC1.TableViewColumn {
                role: "message"
                title: qsTr("备注")
                width: tableView.width * 0.4
              }
            }
          }
        }
      }
      
      SkyCollapse {
        id: cleanAppHistoryCollapse
        anchors.top: cleanFileCollapse.bottom
        anchors.topMargin: 5
        width: parent.width
        onClicked: {
          if (collapseWrapper.index == 1) collapseWrapper.index = -1
          else collapseWrapper.index = 1
          if(root.height < root.maximumHeight) {
            root.height = root.maximumHeight
          }
        }
        iconSource: getIconSource(model.state, model.appHistoryErrorCount, model.appHistoryDone)
        iconColor: getIconColor(model.state, model.appHistoryErrorCount, model.appHistoryDone)
        headerText: {
          if (model.state == CleanFilesWindowViewModel.Success || model.state == CleanFilesWindowViewModel.Error) {
            if (!model.appHistoryErrorCount && !model.appHistoryErrorCount)
              return qsTr("记录已经被清理")
            return qsTr("清理" + model.appHistoryDone + "条记录，成功" + model.appHistorySuccessCount + "条，失败" + model.appHistoryErrorCount + "条")
          }
          return qsTr("正在清理" + model.appHistoryDone + "条记录，成功" + model.appHistorySuccessCount + "条，失败" + model.appHistoryErrorCount + "条")
        }
        descText: qsTr("删除应用软件内最近访问记录")
        progressValue: (model.appHistoryErrorCount + model.appHistorySuccessCount) / model.appHistoryDone
        contentHeight: 200

        Rectangle {
          anchors.fill: parent
          color: "#fcfcfc"
          Connections {
            target: model.appHistoryListModel
            function onRowsInserted() {
              if(model.appHistoryListModel.rowCount() > 0) {
                // 显示 list 列表
                appHistoryListStack.currentIndex = 1
              } else {
                appHistoryListStack.currentIndex = 0
              }
            }
          }
          StackLayout {
            id: appHistoryListStack
            anchors.fill: parent
            currentIndex: {
              return model.appHistoryListModel.rowCount() > 0 ? 1 : 0
            }
            
            SkyEmpty {
              Layout.fillWidth: true
              Layout.fillHeight: true
              size: 50
              fontSize: skyTheme.fontSizeSmall
              text: qsTr("无记录")
            }
            
            Item {
              Layout.fillWidth: true
              Layout.fillHeight: true
            
              SkyList {
                anchors.fill: parent
                model: model.appHistoryListModel
                delegate: Rectangle {
                  width: parent.width
                  height: childrenRect.height + 10
                  color: "#fcfcfc"
                  Item {
                    width: parent.width - 20
                    height: childrenRect.height
                    anchors {
                      left: parent.left
                      leftMargin: 10
                      top: parent
                      topMargin: 5
                      bottomMargin: 5
                    }
                    SkyText {
                      height: contentHeight
                      width: parent.width
                      verticalAlignment: Text.AlignVCenter
                      color: skyTheme.mainFontColor
                      font.pixelSize: skyTheme.fontSize - 1
                      anchors {
                        top: parent
                        topMargin: 5

                      }
                      text: {
                        let result = name + "已清理" + done + "条记录，成功" + successCount + "条";

                        if (errorCount) {
                          result += "，失败" + errorCount + "条；失败原因：" + message;
                        }
                        return qsTr(result);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    
    Item {
      Layout.fillWidth: true
      Layout.preferredHeight: 36
      RowLayout {
        anchors.fill: parent
        // layoutDirection: Qt.RightToLeft
        Item {
          Layout.fillWidth: true
          Layout.fillHeight: true
        }

        Item {
          Layout.preferredWidth: 60
          Layout.fillHeight: true
          SkyButton {
            type: "text"
            text: {
              if (model.state == CleanFilesWindowViewModel.Running)
                return qsTr("中止")
              return qsTr("关闭")
            }
            height: 32
            width: parent.width
            onClicked: {
              root.close()
            }
          }
        }
      }
    }
  }

  function start(fileNames, isCloseApp)
  {
    model.start(fileNames, isCloseApp)
  }

  SkyMessageDialog {
    id: messageDialog
    title: qsTr("中止操作")
    text: qsTr("中止本次清除文件操作?")
    isCancel: true
    isAccept: true
    onAccepted: {
      root.requestClose = true
      model.cancel()
    }
  }

  onClosing: {
    closeShared()
    if (model.state == CleanFilesWindowViewModel.Running)
    {
      messageDialog.open()
      close.accepted = false
    }
  }

  function getIconSource(modelState, errorCount, done) {
    if (modelState == CleanFilesWindowViewModel.Success || modelState == CleanFilesWindowViewModel.Error) {
      if (errorCount == done && done > 0) {
        // 全部失败
        return SkyIcons.StatusErrorFull
      }
      if (errorCount > 0) {
        // 部分失败
        return SkyIcons.InfoSolid
      }

      if (!errorCount && !done) {
        // 没有返回任何数据
        return SkyIcons.InfoSolid
      }

      // 成功
      return SkyIcons.CompletedSolid
    }
    return SkyIcons.Refresh
  }

  function getIconColor(modelState, errorCount, done) {
    if (modelState == CleanFilesWindowViewModel.Success || modelState == CleanFilesWindowViewModel.Error) {
      if (errorCount == done && done > 0) {
        // 全部失败
        return skyTheme.dangerBackground
      }
      if (errorCount > 0) {
        // 部分失败
        return skyTheme.warningBackground
      }

      if (!errorCount && !done) {
        // 没有返回任何数据
        return skyTheme.warningBackground
      }

      // 成功
      return skyTheme.successBackground
    }
    return "#fff"
  }

  Component.onCompleted: {
    if (Qt.platform.os === "linux") {
      root.x = (Screen.width - root.width) / 2
      root.y = (Screen.height - root.height) / 2
    }
  }
}