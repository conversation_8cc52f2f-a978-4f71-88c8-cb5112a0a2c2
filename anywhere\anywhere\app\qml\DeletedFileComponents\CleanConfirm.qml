import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Window 2.15
import QtQuick.Controls.Universal 2.12
import QtQuick.Layouts 1.15
import AnywhereQmlModule 1.0
import SkyUi 1.0

ApplicationWindow {
  id: root
  width: 400
  height: 300
  minimumWidth: 400
  minimumHeight: 200
  title: qsTr("确认清除文件记录")
  flags: Qt.Dialog
  modality: Qt.WindowModal
  visible: true
  property var files: []
  color: "#00000000"
  
  background: Rectangle {
    id: rootBackground
    anchors.fill: parent
    color: "#f5f5f5"
    border.width: 1
    border.color: "#A0A0A0"
  }

  signal confirmed(bool isCloseApp)
  signal cancelled()

  CleanConfirmWindowViewModel {
    id: viewDeletedFileModel

    onFilesChanged: {
      root.files = files
    }
  }

  onFilesChanged: {
    cleanFileListModel.clear()
    for (var i = 0; i < files.length; i++) {
      cleanFileListModel.append({ title: files[i], group: qsTr("待清理文件列表") })
    }
  }

  Item {
    anchors.fill: parent
    ColumnLayout {
      anchors.fill: parent
      anchors.margins: 10
      Item {
        Layout.fillWidth: true
        Layout.fillHeight: true
        
        ListModel {
          id: cleanFileListModel
        }
        ColumnLayout {
          anchors.fill: parent
          spacing: 0
          Item {
            Layout.preferredHeight: 46
            Layout.fillWidth: true
            Layout.leftMargin: 6
            SkyText {
              width: parent.width
              wrapMode: Text.WrapAnywhere
              text: {
                if (viewDeletedFileModel.state === CleanConfirmWindowViewModel.Ready) {
                  return qsTr("将清除以下" + viewDeletedFileModel.files.length + "个文件，清除后将不能再恢复和查看该文件，清理痕迹时不会清理应用内云记录/文档")
                } else if (viewDeletedFileModel.state === CleanConfirmWindowViewModel.Error) {
                  return qsTr("获取要清除的文件列表失败: " + viewDeletedFileModel.reason)
                } else {
                  return qsTr("正在获取要清除的文件列表...")
                }
              }
            }
          }

          Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            clip: true
            color: "#fff"
            radius: 4

            SkyList {
              anchors.fill: parent
              anchors.margins: 8
              skyModel: cleanFileListModel
              skyEnabledBorder: true
              skyTipEnable: false
              visible: viewDeletedFileModel.state === CleanConfirmWindowViewModel.Ready
            }
          }
        }
        // TODO: 这里应该放一个 list 显示要清除的文件列表
      }
      

      Item {
        Layout.fillWidth: true
        Layout.preferredHeight: 36
        RowLayout {
          anchors.fill: parent
          // layoutDirection: Qt.RightToLeft
          Item {
            Layout.fillWidth: true
            Layout.fillHeight: true
          }

          Item {
            Layout.preferredWidth: 80
            Layout.fillHeight: true
            SkyButton {
              type: "primary"
              text: qsTr("开始清理")
              height: 32
              width: parent.width
              onClicked: {
                const message = viewDeletedFileModel.getWarnMessage();
                if (message) {
                  messageDialog.text = qsTr(message);
                  messageDialog.open();
                } else {
                  close()
                  confirmed(false)
                }
              }
            }
          }

          Item {
            Layout.preferredWidth: 60
            Layout.fillHeight: true
            SkyButton {
              type: "text"
              text: qsTr("取消")
              height: 32
              width: parent.width
              onClicked: {
                close()
                cancelled()
              }
            }
          }
        }
      }
    }
  }

  SkyMessageDialog {
    id: messageDialog
    title: qsTr("警告")
    text: qsTr("后续设置")
    acceptText: qsTr("同意")
    cancelText: qsTr("拒绝")
    onAccepted: {
      root.close()
      confirmed(true);
    }
    onRejected: {
      root.close()
      confirmed(false);
    }
  }
  function init(files) {
    viewDeletedFileModel.init(files)
  }

  onClosing: {
    closeShared()
  }

  Component.onCompleted: {
    if (Qt.platform.os === "linux") {
      root.x = (Screen.width - root.width) / 2
      root.y = (Screen.height - root.height) / 2
    }
  }
}