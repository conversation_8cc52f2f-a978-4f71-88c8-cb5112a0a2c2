// TODO)) 和QC1.TableViewColumn 一一对应
let COLUMN_INFO = [
  {
    name: "name",
    sortName: "name",
    title: "名称",
    sortable: false,
    sortOrder: null,
    enableBorder: true,
  },
  {
    name: "successStr",
    sortName: "success",
    title: "处理结果",
    sortable: false,
    sortOrder: null,
    enableBorder: true,
  },
  {
    name: "message",
    sortName: "message",
    title: "备注",
    sortable: false,
    sortOrder: null,
    enableBorder: true,
  }
];


let APP_HISTORY_COLUMN_INFO = [
  {
    name: "name",
    sortName: "name",
    title: "名称",
    sortable: false,
    sortOrder: null,
    enableBorder: true,
  },
  {
    name: "successStr",
    sortName: "success",
    title: "处理结果",
    sortable: false,
    sortOrder: null,
    enableBorder: true,
  },
  {
    name: "successCut",
    sortName: "successCut",
    title: "成功数量",
    sortable: false,
    sortOrder: null,
    enableBorder: true,
  },
  {
    name: "errorCut",
    sortName: "errorCut",
    title: "失败数量",
    sortable: false,
    sortOrder: null,
    enableBorder: true,
  },
  {
    name: "message",
    sortName: "message",
    title: "备注",
    sortable: false,
    sortOrder: null,
    enableBorder: true,
  }
];
