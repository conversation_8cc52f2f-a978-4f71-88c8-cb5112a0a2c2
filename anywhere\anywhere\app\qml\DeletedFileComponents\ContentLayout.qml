import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Controls.Universal 2.12
import QtQuick.Layouts 1.15
import "./" as Components
import AnywhereQmlModule 1.0
import QtQuick.Controls 1.2 as QC1
import QtGraphicalEffects 1.15
import SkyUi 1.0

Item {
  id: root
  width: parent.width
  height: parent.height
  
  property string fileNameQuery: ""
  property string fullTextQuery: ""
  property bool caseInsensiveQuery: true
  property bool searchOnlyFileNameQuery: false
  property string sortPropertyQuery: ""
  property bool sortOrderAscQuery: false

  // 是否自动打开右侧内容详情
  property bool autoExpand: true
  signal searchChange(string fileName, string fullText, bool caseInsensive, bool searchOnlyFileName,
                      string deleteTimeFrom, string deleteTimeTo, string fileSizeMin, string fileSizeMax)

  SkyFileMenu {
    id: skyFileMenu

    onCleanFilesRequested: {
      root.cleanFiles(files)
    }

    onMessageChanged: {
      if (message) {
        showWarning(message)
      }
    }
  }

  Connections {
    target: viewDeletedFileModel
    function onSearchingChanged() {
      contentPreviewer.activeDocId = viewDeletedFileModel.invalidDocId
    }

    function onActiveDocIdChanged() {
      contentPreviewer.activeDocId = viewDeletedFileModel.activeDocId
    }
  }
  // @@@@liukai 掉用viewDeletedFileModel的search方法
  onSearchChange: {
    root.fileNameQuery = fileName
    root.fullTextQuery = fullText
    root.caseInsensiveQuery = caseInsensive
    root.searchOnlyFileNameQuery = searchOnlyFileName
    var sortProperty = root.sortPropertyQuery ? root.sortPropertyQuery : (fullText ? "text": "")
    var sortOrderAsc = root.sortOrderAscQuery

    // 搜索开始时直接折叠preview
    contentPreviewerContainer.isVisible = false

    // 调用带筛选条件的搜索方法
    viewDeletedFileModel.searchDeletedFilesWithFilters(fileName, fullText, sortProperty, sortOrderAsc, caseInsensive, searchOnlyFileName,
                                                       deleteTimeFrom, deleteTimeTo, fileSizeMin, fileSizeMax)
  }

  // 左右分栏
  SplitView {
    anchors.fill: parent
    orientation: Qt.Horizontal
    id: splitView
    handle: SkySplitHandle {
      id: splitHandle
      visible: contentPreviewerContainer.isVisible
    }
    
  
    // 左侧区域
    Item {
      id: leftContainer
      SplitView.preferredWidth: contentPreviewerContainer.afterVisibled ? splitView.width * 0.7 : splitView.width
      SplitView.minimumWidth: 500
      // radius: 8
      // Rectangle {
      //   anchors.fill: parent
      //   color: "#fff"
      //   radius: 8
      //   layer.enabled: true
      //   layer.effect: DropShadow {
      //     transparentBorder: true
      //     horizontalOffset: -0
      //     verticalOffset: -0
      //     radius: 12
      //     samples: 16
      //     color: "#333333cc"
      //   }
      // }
      ColumnLayout {
        anchors.fill: parent
        spacing: 0

        // 顶部搜索栏
        // 部分win11 滚动表格时, 会有内容阴影, 即使添加 clip 也无法将其裁剪掉
        // 我们将搜索结果置于最底层, 这样我们就可以避免其出现内容阴影
        Rectangle {
          Layout.preferredWidth: parent.width + 20
          Layout.leftMargin: -10
          Layout.preferredHeight: {
            var baseHeight = contentPreviewerContainer.isVisible ? 91 : 50
            // 如果高级筛选展开，增加高度
            if (searchFrom.showAdvancedFilters) {
              baseHeight += 60  // 高级筛选区域的高度
            }
            return baseHeight
          }
          color: "#f5f5f5"

          Item {
            width: parent.width - 30
            height: 30
            y: 10
            x: 10
            // @@@@liukai 文件内容检查-搜索栏控件
            Components.SearchFrom {
              id: searchFrom
              previewerEnabled: contentPreviewerContainer.isVisible
              caseInsensive: root.caseInsensiveQuery
              searchFullPath: viewDeletedFileModel.searchFullPath
              enableCompression: viewDeletedFileModel.enableCompression
              filenamesHistory: viewDeletedFileModel.filenamesHistory.map(v => {
                return {
                  title: v,
                  group: "历史记录",
                  disabled: false
                }
              })
              fullTextsHistory: viewDeletedFileModel.fullTextsHistory.map(v => {
                return {
                  title: v,
                  group: "历史记录",
                  disabled: false
                }
              })
              onFormChage: (fileName, fullText, caseInsensive, searchOnlyFileName,
                            deleteTimeFrom, deleteTimeTo, fileSizeMin, fileSizeMax) => {
                if (/[\*,\?]/.test(fileName) || /[\*,\?]/.test(fullText)) {
                  showWarning("不支持通配符")
                  return
                }

                // 验证删除时间格式
                if (deleteTimeFrom && !/^\d{4}-\d{2}-\d{2}$/.test(deleteTimeFrom)) {
                  showWarning("删除时间开始日期格式不正确，请使用 yyyy-MM-dd 格式")
                  return
                }
                if (deleteTimeTo && !/^\d{4}-\d{2}-\d{2}$/.test(deleteTimeTo)) {
                  showWarning("删除时间结束日期格式不正确，请使用 yyyy-MM-dd 格式")
                  return
                }

                // 验证文件大小格式 - 支持单位输入
                if (fileSizeMin && !/^[0-9]*\.?[0-9]*\s*[a-zA-Z]*$/.test(fileSizeMin.trim())) {
                  showWarning("最小文件大小格式不正确，支持格式：1024、1KB、1.5MB、2GB等")
                  return
                }
                if (fileSizeMax && !/^[0-9]*\.?[0-9]*\s*[a-zA-Z]*$/.test(fileSizeMax.trim())) {
                  showWarning("最大文件大小格式不正确，支持格式：1024、1KB、1.5MB、2GB等")
                  return
                }

                if (fullText && fileName.trim() == "") {
                  if (viewDeletedFileModel.enableCompression) {
                    fileName = ".doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf .7z .rar .zip"
                    showInfo("文件名搜索框已为您填充常用文档及压缩包(.doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf .7z .rar .zip)")
                  } else {
                    fileName = ".doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf"
                    showInfo("文件名搜索框已为您填充常用文档（.doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf）")
                  }
                }
                searchFrom.fileNameQuery = fileName
                root.searchChange(fileName, fullText, caseInsensive, searchOnlyFileName,
                                  deleteTimeFrom, deleteTimeTo, fileSizeMin, fileSizeMax)
              }
              onSetEnableCompression: (enableCompression) => {
                viewDeletedFileModel.setEnableCompression(enableCompression)
              }
              onPreviewShouldCollapse: {
                // 停止过滤时折叠preview
                contentPreviewerContainer.isVisible = false
              }
            }
          }
        }

        // 中部搜索结果
        Rectangle {
          Layout.alignment: Qt.AlignLeft
          Layout.preferredWidth: parent.width - Layout.rightMargin - Layout.leftMargin
          Layout.fillHeight: true
          Layout.leftMargin: 0
          Layout.rightMargin: 0
          Layout.bottomMargin: 30
          color: "transparent"
          z: -1
          // 内容区域纵向分割
          // 内容区域纵向分割
          SkyCard {
            anchors.fill: parent
            paddingValue: 0
            marginValue: 0
            radiusValue: 0
            content: Components.SearchResultTable {
              anchors.fill: parent
              id: searchResultTable
              hits: viewDeletedFileModel.topHits
              activeDocId: viewDeletedFileModel.activeDocId
              onDocSelected: {
                contentPreviewerContainer.isVisible = true
                if (root.autoExpand) {
                  // contentPreviewerContainer.isVisible = true
                  root.autoExpand = false
                }
                viewDeletedFileModel.activeDocId = docId
                contentPreviewer.activeDocId = docId
              }
            }
          }
        }

        // // 底部状态栏
        // Rectangle {
        //   Layout.alignment: Qt.AlignLeft
        //   Layout.preferredWidth: parent.width - Layout.rightMargin - Layout.leftMargin
        //   Layout.preferredHeight: 30
        //   Layout.leftMargin: -10
        //   Layout.rightMargin: -10
        //   // color: "transparent"
        //   color: "#ECEDEF"
        //   Components.FooterStatusWidget {
            
        //   }
        // }
      }

      Button {
        width: 15
        height: 40
        background: Rectangle {
          color: "#fff"
          radius: 4
        }
        y: (parent.height - height) / 2
        x: {
          if (contentPreviewerContainer.isVisible) return parent.width - 15
          return parent.width - 5
        }
        
        SkyIcon {
          anchors.centerIn: parent
          font.pixelSize: 14
          enabledClick: false
          iconSource: {
            if (contentPreviewerContainer.isVisible) return SkyIcons.ChevronRightSmall
            return SkyIcons.ChevronLeftSmall
          }
        }

        MouseArea {
          anchors.fill: parent
          enabled: false
          cursorShape: Qt.PointingHandCursor
        }

        onClicked: {
          contentPreviewerContainer.isVisible = !contentPreviewerContainer.isVisible
        }
      }

      state: contentPreviewerContainer.isVisible
      states: [
        State { name: "true"; PropertyChanges { target: leftContainer; width: splitView.width * 0.7 } },
        State { name: "false"; PropertyChanges { target: leftContainer; width: splitView.width } }
      ]
      transitions: Transition {
        to: "*"
        reversible: true
        NumberAnimation { properties: "width"; duration: 300 }
      }
    }

    // 右侧内容详情
    Rectangle {
      id: contentPreviewerContainer
      SplitView.preferredWidth: contentPreviewerContainer.afterVisibled ? splitView.width * 0.3 : 0
      SplitView.minimumWidth: contentPreviewerContainer.afterVisibled ? 200: 0
      property bool isVisible: false
    
      property bool afterVisibled: false
      x: splitView.width * 2
      opacity: 1
      color: "#fff"
      radius: 8
      visible: {
        return afterVisibled || isVisible
      }
      Rectangle {
        anchors.fill: parent
        anchors.rightMargin: -10
        anchors.leftMargin: -10
        color: "#fff"
      }

      Rectangle {
        anchors.fill: parent
        anchors.topMargin: 10
        anchors.bottomMargin: 40
        Components.ContentPreviewer {
          id: contentPreviewer
          anchors.fill: parent
          query: viewDeletedFileModel.fullTextQuery
          invalidDocId: viewDeletedFileModel.invalidDocId
          caseInsensiveQuery: root.caseInsensiveQuery
          isCreate: contentPreviewerContainer.isVisible
        }
      }

      onIsVisibleChanged: {
        visibilityTimer.start()
      }

      Timer {
        id: visibilityTimer
        interval: 300 // 于动画时间保持一致
        onTriggered: {
          contentPreviewerContainer.afterVisibled = contentPreviewerContainer.isVisible
          visibilityTimer.stop();
        }
      }

      state: contentPreviewerContainer.isVisible
      states: [
        State { name: "true"; PropertyChanges { target: contentPreviewerContainer; x: splitView.width - contentPreviewerContainer.width; opacity: 1; width: splitView.width * 0.3 } },
        State { name: "false"; PropertyChanges { target: contentPreviewerContainer; x: splitView.width; opacity: 0; width: splitView.width * 0 } }
      ]
      transitions: Transition {
        to: "*"
        reversible: true
        NumberAnimation { properties: "x,opacity,width"; duration: 300 }
      }
    }
  }

  // 右侧顶部标题栏
  Rectangle {
    height: 40
    width: contentPreviewerContainer.width + 20
    color: "#1890FF"
    y: -40
    x: contentPreviewerContainer.x - 10
    visible: {
      return contentPreviewerContainer.afterVisibled || contentPreviewerContainer.isVisible
    }
    SkyText {
      id: title
      text: searchResultTable.activedFileName ? "当前预览: " + searchResultTable.activedFileName: ""
      anchors.centerIn: parent
      color: "#fff"
      width: parent.width - 20
      x: 10
      elide: Text.ElideRight
      wrapMode: Text.NoWrap
    }
  }

  // 底部状态栏
  Rectangle {
    height: 30
    anchors.bottom: parent.bottom
    x: -10
    width: parent.width + 20
    // color: "transparent"
    color: "#ECEDEF"
    Rectangle {
      anchors.top: parent.top
      height: 1
      width: parent.width
      color: "#D1D1D1"
    }
    
    Components.FooterStatusWidget {
      
    }
  }

  // 清除文件, 弹出清除确认对话框
  function cleanFiles(fileNames) {
    let component = Qt.createComponent("./CleanConfirm.qml");
    let confirmPopup = component.createObject(root);
    confirmPopup.init(fileNames)
    openShared();
    
    confirmPopup.confirmed.connect(function(isCloseApp) {
      let files = confirmPopup.files
      let component = Qt.createComponent("./Clean.qml");
      let popup = component.createObject(root);
      popup.start(files, isCloseApp);
      openShared();
    })
  }
}