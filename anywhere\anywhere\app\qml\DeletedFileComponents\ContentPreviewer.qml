import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Controls.Universal 2.12
import QtQuick.Layouts 1.15
import AnywhereQmlModule 1.0
import SkyUi 1.0


Rectangle {
  id: priviewRoot
  color: "transparent"
  property int activeDocId
  property string query
  property bool caseInsensiveQuery: false
  property bool isCreate: false
  property int invalidDocId

  onActiveDocIdChanged: {
    priviewLoader.activeDocId = activeDocId
  }

  onQueryChanged: {
    priviewLoader.fullTextQuery = query
  }

  onCaseInsensiveQueryChanged: {
    priviewLoader.caseInsensiveQuery = caseInsensiveQuery
  }

  onIsCreateChanged: {
    if (isCreate) {
      priviewLoader.invalidDocId = invalidDocId
      priviewLoader.fullTextQuery = query
      priviewLoader.caseInsensiveQuery = caseInsensiveQuery
      priviewLoader.sourceComponent = priviewComponent
    } else {
      priviewLoader.sourceComponent = undefined
    }
  }

  Loader {
    id: priviewLoader
    anchors.fill: parent

    property int activeDocId
    property string fullTextQuery
    property bool caseInsensiveQuery
    property int invalidDocId
  }

  Component {
    id: priviewComponent
    Item {
      anchors.fill: parent

      ListModel {
        id: summaryModel
      }

      ListModel {
        id: keywordModel
      }

      DeletedContentPreviewViewModel {
        id: contentPreviewViewModel
        docId: activeDocId
        query: fullTextQuery
        caseInsensive: caseInsensiveQuery
        onTextStateChanged: function(textState) {
          // resetActiveTabIndex 会修改 currentIndex 的值
          if (contentPreviewViewModel.imageState == DeletedContentPreviewViewModel.UNAVAILABLE || contentPreviewViewModel.imageState == DeletedContentPreviewViewModel.INIT) {
            previewBar.currentIndex = 0
          }
        }

        onImageStateChanged: function(imageState) {
          // resetActiveTabIndex 会修改 currentIndex 的值
          if (contentPreviewViewModel.textState == DeletedContentPreviewViewModel.UNAVAILABLE || contentPreviewViewModel.textState == DeletedContentPreviewViewModel.INIT) {
            previewBar.currentIndex = 1
          }
        }

        onSummaryListChanged: {
          summaryModel.clear()
          for (var i = 0; i < contentPreviewViewModel.summaryList.length; i++) {
            summaryModel.append({
              title: contentPreviewViewModel.summaryList[i].text,
              startPos: contentPreviewViewModel.summaryList[i].startPos,
              length: contentPreviewViewModel.summaryList[i].length,
              keywords: contentPreviewViewModel.summaryList[i].keywords
            })
          }
        }

        onKeywordListChanged: {
          keywordModel.clear()
          for (var i = 0; i < contentPreviewViewModel.keywordList.length; i++) {
            keywordModel.append({
              title: contentPreviewViewModel.keywordList[i].keyword + "   " + contentPreviewViewModel.keywordList[i].count + "次"
            })
          }
        }

        function resetActiveTabIndex() {
          if (contentPreviewViewModel.textState == DeletedContentPreviewViewModel.UNAVAILABLE && previewBar.currentIndex == 0) {
            previewBar.currentIndex = 1
          }
          if (contentPreviewViewModel.imageState == DeletedContentPreviewViewModel.UNAVAILABLE && previewBar.currentIndex == 1) {
            previewBar.currentIndex = 0
          }
        }
      }

      ColumnLayout {
        anchors.fill: parent

        TabBar {
          id: previewBar
          Layout.fillWidth: true
          Layout.preferredHeight: 30
          SkyTabBar {
            title: qsTr("内容详情")
            active: previewBar.currentIndex == 0
            anchors.bottom: parent.bottom
          }
          SkyTabBar {
            title: qsTr("预览图片")
            active: previewBar.currentIndex == 1
            anchors.bottom: parent.bottom
          }
        }

        Connections {
          target: previewBar
          function onCurrentIndexChanged() {
            // 当切换标签页时, 触发相应标签页的数据加载
            if (previewBar.currentIndex == 0) {
              contentPreviewViewModel.loadText();
            } else if (previewBar.currentIndex == 1) {
              contentPreviewViewModel.loadImage();
            }
          }
        }

        // 内容摘要 / 预览
        StackLayout {
          Layout.fillWidth: true
          Layout.fillHeight: true
          currentIndex: previewBar.currentIndex
          // 内容摘要
          Item {
            StackLayout {
              anchors.fill: parent
              currentIndex: {
                if (activeDocId == invalidDocId) return 1;
                if (contentPreviewViewModel.textState == DeletedContentPreviewViewModel.DONE && !!contentPreviewViewModel.content) {
                  return 0;
                }
                return 1;
              }
              SplitView {
                id: textView
                Layout.fillWidth: true
                Layout.fillHeight: true
                orientation: Qt.Vertical
                handle: SkySplitHandle {
                  skyOrientation: Qt.Vertical
                  color: "#F5F5F5"
                  showHanld: true
                }
                Rectangle {
                  SplitView.preferredHeight: parent.height * 0.7
                  SplitView.preferredWidth: parent.width
                  SplitView.minimumHeight: 100
                  color: "transparent"
                  Rectangle {
                    id: textEditWrapper
                    anchors.fill: parent
                    color: "transparent"

                    QTextItem {
                      id: textEdit
                      width: textEditWrapper.width - textEditScroll.width
                      height: viewportHeight
                      viewportHeight: textEditWrapper.height
                      hitPositions: contentPreviewViewModel.matchData
                      text: {
                        return qsTr(contentPreviewViewModel.content)
                      }
                      // textFormat: TextEdit.RichText
                      // wrapMode: TextEdit.WrapAnywhere

                      font.family: skyTheme.fontFamily
                      font.pixelSize: skyTheme.fontSize
                      font.letterSpacing: 2
                      font.weight: Font.Thin
                      // color: "#000"
                      onTextChanged: {
                        empty.isLoading = false;
                      }
                      onMessageChanged: {
                        showWarning(message)
                      }
                      onContentTopChanged: {
                        textEditScroll.position = textEdit.contentTop / textEdit.contentHeight
                      }
                    }

                    Connections {
                      target: contentPreviewerContainer
                      function onWidthChanged() {
                        if (textEditScroll.position === 0) return;
                        if(!textEditScroll.visible) {
                          textEditScroll.position = 0
                          return;
                        }

                        if (textEdit.contentTop + textEdit.height > textEdit.contentHeight) {
                          textEdit.contentTop = textEdit.contentHeight - textEdit.height
                        }
                      }
                    }
                    SkyScrollBar {
                      id: textEditScroll
                      hoverEnabled: true
                      active: hovered || pressed
                      orientation: Qt.Vertical
                      size: parent.height / textEdit.contentHeight
                      anchors.top: parent.top
                      anchors.right: parent.right
                      height: parent.height
                      scrollBarSize: 12
                      barOpacity: 0.6
                      barBackground: "#b7b7b7"
                      visible: parent.height / textEdit.contentHeight < 1
                      onPositionChanged: {
                        textEdit.contentTop = position * textEdit.contentHeight
                      }

                      background: Rectangle {
                        implicitWidth: 12
                        color: skyTheme.scrollBackground
                        Item {
                          width: 11
                          anchors.top: parent.top
                          anchors.right: parent.right
                          height: parent.height - 5
                          anchors.rightMargin: 3
                          anchors.topMargin: 2
                          QPreviewItem {
                            id: previewItem
                            anchors.fill: parent
                            lineDataList: textEdit.lineDataList
                            onItemClicked: {
                              textEdit.jumpToSnippet(data.startPos, data.endPos - data.startPos);
                            }
                          }
                          visible: textEditScroll.visible
                        }
                      }
                    }
                  }
                }
                Rectangle {
                  SplitView.preferredHeight: parent.height * 0.3
                  SplitView.preferredWidth: parent.width
                  SplitView.minimumHeight: 100
                  color: "transparent"
                  visible: !!contentPreviewViewModel.keywordList.length
                  Item {
                    anchors.fill: parent
                    TabBar {
                      id: summaryBar
                      width: parent.width
                      height: 30
                      SkyTabBar {
                        title: qsTr("摘要详情")
                        active: summaryBar.currentIndex == 0
                        anchors.bottom: parent.bottom
                      }
                      SkyTabBar {
                        title: qsTr("关键词列表")
                        active: summaryBar.currentIndex == 1
                        anchors.bottom: parent.bottom
                      }
                    }

                    // 摘要详情 / 关键词列表
                    StackLayout {
                      width: parent.width
                      height: parent.height - summaryBar.height
                      currentIndex: summaryBar.currentIndex
                      anchors.top: summaryBar.bottom
                      Item {
                        id: summaryTab
                        clip: true
                        SkyList {
                          skyModel: summaryModel
                          anchors.fill: parent
                          skyEnableMouseArea: true
                          skyEnabledBorder: true
                          onItemClicked: {
                            if (textEditScroll.visible)
                            {
                              const keyword = item.keywords.get(0);
                              if (!keyword) {
                                textEdit.jumpToSnippet(item.startPos, item.length)
                              } else {
                                textEdit.jumpToSnippet(keyword.startPos, keyword.length)
                              }
                            }
                          }
                        }
                      }
                      Item {
                        id: keywordTab
                        SkyList {
                          skyModel: keywordModel
                          anchors.fill: parent
                          skyEnableMouseArea: false
                          skyEnabledBorder: true
                        }
                      }
                    }
                  }
                }
              }

              SkyEmpty {
                id: empty
                Layout.fillWidth: true
                Layout.fillHeight: true
                size: 100
                fontSize: skyTheme.fontSizeSmall
                isLoading: (contentPreviewViewModel.textState === DeletedContentPreviewViewModel.LOADING)
                text: {
                  switch (contentPreviewViewModel.textState) {
                    case DeletedContentPreviewViewModel.INIT:
                    return qsTr("暂无数据")
                    case DeletedContentPreviewViewModel.LOADING:
                    return qsTr("加载中...")
                    case DeletedContentPreviewViewModel.DONE:
                    return qsTr("没有提取到有效内容")
                    case DeletedContentPreviewViewModel.UNAVAILABLE:
                    return qsTr("不支持的格式")
                    case DeletedContentPreviewViewModel.NOT_EXIST:
                    return qsTr("文件不存在")
                    default:
                    return qsTr("")
                  }
                }
              }
            }
          }
          // 预览图片
          Item {
            StackLayout {
              anchors.fill: parent
              currentIndex: {
                if (activeDocId == invalidDocId) return 1;
                if (contentPreviewViewModel.imageState == DeletedContentPreviewViewModel.DONE) {
                  return 0;
                }
                return 1
              }
              Image {
                Layout.fillWidth: true
                Layout.fillHeight: true
                source: contentPreviewViewModel.image
                asynchronous: true
                cache: false
                autoTransform: true
                fillMode: Image.PreserveAspectFit
                Button {
                  anchors.fill: parent
                  visible: contentPreviewViewModel.imageState == DeletedContentPreviewViewModel.DONE
                  text: qsTr("")
                  background: Rectangle {
                    color: "transparent"
                  }
                  onDoubleClicked: {
                    skyFileMenu.openFile(contentPreviewViewModel.filePath)
                  }
                }
              }
              SkyEmpty {
                id: imageEmpty
                Layout.fillWidth: true
                Layout.fillHeight: true
                size: 100
                fontSize: skyTheme.fontSizeSmall
                isLoading: (contentPreviewViewModel.imageState === DeletedContentPreviewViewModel.LOADING)
                text: {
                  switch (contentPreviewViewModel.imageState) {
                    case DeletedContentPreviewViewModel.INIT:
                    return qsTr("暂无数据")
                    case DeletedContentPreviewViewModel.LOADING:
                    return qsTr("加载中...")
                    case DeletedContentPreviewViewModel.DONE:
                    return qsTr("完成")
                    case DeletedContentPreviewViewModel.UNAVAILABLE:
                    return qsTr("没有预览, 当前仅支持预览图片文件")
                    default:
                    return qsTr("")
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}