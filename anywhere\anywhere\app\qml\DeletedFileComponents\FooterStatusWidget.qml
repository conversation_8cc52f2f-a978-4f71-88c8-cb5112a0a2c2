import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.15
import SkyUi 1.0
import AnywhereQmlModule 1.0


Rectangle {
  id: footControl
  anchors.fill: parent
  color: "transparent"
  
  property int pendingMaxSize: 0

  signal viewClicked()

  Rectangle {
    height: 1
    width: parent.width
    color: "#D1D1D1"
  }

  SkyProgress {
    z: 2
    y: 0 - height
    width: parent.width
    height: 4
    value: viewDeletedFileModel.indexProgress
    visible: {
      return false
      // return viewModel.indexState === MainWindowViewModel.Loading || viewModel.indexState === MainWindowViewModel.Error;
    }
  }

  Connections {
    target: viewDeletedFileModel
    function onPendingFilesSetChanged() {
      footControl.pendingMaxSize = viewDeletedFileModel.pendingFilesSet.count;
    }

    function onTopHitsChanged() {
    }
  }

  RowLayout {
    anchors.fill: parent
    spacing: 0

    Item {
      Layout.fillHeight: true
      Layout.preferredWidth: height
      Rectangle {
        anchors.fill: parent
        anchors.topMargin: 1
        color: "transparent"
        SkyIcon {
          id: stateIcon
          iconSource: {
            if (viewDeletedFileModel.pendingFilesSet && !!viewDeletedFileModel.pendingFilesSet.count && viewDeletedFileModel.filterState != 3) {
              return SkyIcons.Sync
            }
            if ((viewDeletedFileModel.indexState === MainWindowViewModel.Success && !viewDeletedFileModel.searching) || viewDeletedFileModel.filterState === 3)
              return SkyIcons.Ringer
            if (viewDeletedFileModel.indexState === MainWindowViewModel.Error)
              return SkyIcons.SyncError
            if (!viewDeletedFileModel.searching)
              return SkyIcons.Ringer
            return SkyIcons.Sync
          }
          color: "#666"
          font.pixelSize: skyTheme.fontSizeLarge
          enabledClick: false
          anchors.centerIn: parent
          verticalAlignment: Text.AlignVCenter
        }
        RotationAnimation{
          id: rotationAnimation
          target: stateIcon
          to: 360
          from: 0
          direction: RotationAnimation.Clockwise
          running: viewDeletedFileModel.searching
          duration: 800
          loops: Animation.Infinite
          onStopped: {
            stateIcon.rotation = 0
          }
        }
      }
      MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
          toolTip.visible = true
        }
        onExited: {
          toolTip.visible = false
        }
      }
      // SkyTooltip {
      //   id: toolTip
      //   visible: false
      //   text: {
      //     if (viewDeletedFileModel.indexState === MainWindowViewModel.Error) return "读取文件列表失败： " + viewDeletedFileModel.indexErrorReason;
      //     return "文件总数" + (viewDeletedFileModel.indexedFilesCnt ? viewDeletedFileModel.indexedFilesCnt : "0") + "个"
      //   }
      // }
    }

    Item {
      Layout.fillHeight: true
      Layout.fillWidth: true
      // visible: (viewDeletedFileModel.topHits && viewDeletedFileModel.topHits.size) || viewDeletedFileModel.indexState === MainWindowViewModel.Success || viewDeletedFileModel.searching
      visible: true
      SkyText {
        height: parent.height
        verticalAlignment: Text.AlignVCenter
        color: "#666"
        // anchors.centerIn: parent
        text: {
          if (viewDeletedFileModel.indexState === MainWindowViewModel.Loading && viewDeletedFileModel.searching) {
            return "正在加载...";
          } else if (viewDeletedFileModel.filterState === 2) {
            return "正在停止搜索，请稍后...";
          } else if (viewDeletedFileModel.filterState === 3) {
            return "已停止搜索";
          } else if ((!viewDeletedFileModel.searching && viewDeletedFileModel.filterState === 0 && viewDeletedFileModel.indexState != MainWindowViewModel.Success)) {
            return "已就绪";
          } else if (viewDeletedFileModel.pendingFilesSet && !viewDeletedFileModel.pendingFilesSet.count && !viewDeletedFileModel.searching) {
            var fileCount = viewDeletedFileModel.topHits ? viewDeletedFileModel.topHits.size : 0
            if (fileCount >= 200000) {
              return "搜索完成，搜索到200000+个文件，请优化搜索条件"
            }
            return "搜索完成, 搜索到" + fileCount + "个文件 ";
          } else if ((viewDeletedFileModel.topHits && !viewDeletedFileModel.topHits.size)) {
            return "正在搜索";
          } else {
            var currentFileCount = viewDeletedFileModel.topHits ? viewDeletedFileModel.topHits.size : 0
            if (currentFileCount >= 200000) {
              return "正在搜索，匹配到200000+个文件，请优化搜索条件";
            }
            return "正在搜索，匹配到" + currentFileCount + "个文件";
          }
        }
      }
    }

    Item {
      Layout.fillHeight: true
      Layout.fillWidth: true
      visible: false //viewDeletedFileModel.indexState === MainWindowViewModel.Loading && !viewDeletedFileModel.searching

      SkyText {
        height: parent.height
        verticalAlignment: Text.AlignVCenter
        color: "#666"
        // anchors.centerIn: parent
        text: "正在读取文件列表，请稍后"
      }
    }

    // 计算距索引建立所用时间
    Timer {
      id: timer
      interval: 10000
      running: true
      repeat: true
      property int elapsedTime: 0
      property string elapsedStr: ""
      onTriggered: elapsedTime = viewDeletedFileModel.elapsedSecondsSinceIndexUpdated();
      onElapsedTimeChanged: {
        const elapsedMinute = Math.floor(elapsedTime / 60);
        const days = Math.floor(elapsedMinute / (60 * 24));
        const hours = Math.floor((elapsedMinute % (60 * 24)) / 60);
        const mins = elapsedMinute % 60;
        elapsedStr = (days > 0 ? days + "天":"") + (hours > 0 ? hours + "小时":"") + (mins > 0 ? mins + "分钟":"")
      }
    }

    // 展示距索引建立所用时间
    Item {
      id: time
      Layout.fillHeight: true
      Layout.preferredWidth: timeIcon.width + timeText.contentWidth
      Layout.rightMargin: 32
      Layout.leftMargin: 32
      visible: false//timer.elapsedTime && timer.elapsedStr

      Item {
        id: timeIcon
        height: parent.height
        width: height
        anchors.topMargin: 1
        anchors.top: parent.top
        SkyIcon {
          iconSource: SkyIcons.Stopwatch
          color: "#666"
          font.pixelSize: skyTheme.fontSizeLarge
          enabledClick: false
          anchors.centerIn: parent
          verticalAlignment: Text.AlignVCenter
        }
      }

      SkyText {
        id: timeText
        anchors.left: timeIcon.right
        height: parent.height
        verticalAlignment: Text.AlignVCenter
        color: "#666"
        text: timer.elapsedStr + "前"
      }

      MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
          timeToolTip.visible = true;
        }
        onExited: {
          timeToolTip.visible = false;
        }
      }
      
      SkyTooltip {
        id: timeToolTip
        text: {
          return "文件列表首次加载于" + timer.elapsedStr + "前"
        }
      }
    }

    Item {
      id: pendingProgress
      Layout.fillHeight: true
      Layout.preferredWidth: 100
      Layout.rightMargin: 32
      visible:  false
      
      SkyProgress {
        width: parent.width
        height: 2
        anchors.centerIn: parent
        value: {
          return 0
        }
      }

      SkyTooltip {
        id: pendingTooltip
        visible: pendingProgress.visible
        text: viewDeletedFileModel.filterState === 3 ? "已停止搜索" : "<b>后台搜索仍在进行中...</b>"
      }
      
      MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
          pendingTooltip.visible = true;
        }
        onExited: {
          pendingTooltip.visible = false;
        }
      }
    }
  }
}  