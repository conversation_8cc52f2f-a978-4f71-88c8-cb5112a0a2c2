import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Controls.Universal 2.12
import QtQuick.Layouts 1.15
import QtQuick.Controls 1.2 as QC1
import QtQuick.Controls.Styles 1.4 as QC1Styles
import AnywhereQmlModule 1.0
import SkyUi 1.0
import "SearchResultTableUtils.js" as Utils

Rectangle {
  id: root
  property QTopHits hits
  property int activeDocId
  property string activedFileName: ""

  property var activedFilePaths_: []

  // 用于跟踪点击状态和焦点状态
  property int lastClickedRow: -1
  property bool isTableFocused: false

  // 分页相关属性
  property int pageSize: 100
  property int currentPage: 1
  property int totalItems: 0
  property int totalPages: 0

  signal docSelected(docId: int)
  color: "transparent"

  SearchResultTableModel {
    id: searchControlsModel
  }

  // 分页数据模型
  PaginatedSearchResultTableModel {
    id: paginatedModel
    pageSize: root.pageSize

    onTotalItemsChanged: {
      root.totalItems = totalItems
    }

    onTotalPagesChanged: {
      root.totalPages = totalPages
    }

    onCurrentPageChanged: {
      root.currentPage = currentPage
      tableView.currentRow = -1
    }
  }

  onHitsChanged: {
    searchControlsModel.topHits = root.hits
    paginatedModel.topHits = root.hits
  }

  // 切换页面
  function goToPage(page) {
    paginatedModel.currentPage = page
  }

  Connections {
    target: viewDeletedFileModel
    function onSearchingChanged() {
      tableView.currentRow = -1
    }
  }

  StackLayout {
    id: stackLayout
    anchors.fill: parent
    currentIndex: {
      if (!viewDeletedFileModel.searching) {
        return 0
      }
      if (viewDeletedFileModel.searching && viewDeletedFileModel.indexState == MainWindowViewModel.Loading) {
        return 1
      }

      if (viewDeletedFileModel.indexState == MainWindowViewModel.Success || viewDeletedFileModel.indexState == MainWindowViewModel.Error) {
        if (searchControlsModel.topHits && searchControlsModel.topHits.size == 0) return 1
      }
      return 0
    }

    // 有效数据
    Item {
      Layout.fillWidth: true
      Layout.fillHeight: true

      Item {
        anchors.fill: parent

        SkyTable {
          id: tableView
          anchors.top: parent.top
          anchors.left: parent.left
          anchors.right: parent.right
          anchors.bottom: parent.bottom
          anchors.bottomMargin: 50  // 给分页栏留出足够空间
          tableModel: paginatedModel
          sortColumns: Utils.COLUMN_INFO
          focus: true

          // 跟踪焦点状态
          onActiveFocusChanged: {
            root.isTableFocused = activeFocus
          }
          onCurrentRowChanged: {
            // 只有当表格有焦点时，焦点变化才触发preview
            if (tableView.isTableFocused && currentRow >= 0) {
              docSelected(paginatedModel.docId(currentRow))
              root.activedFileName = paginatedModel.data(paginatedModel.index(tableView.currentRow, 0), 259)
            }
          }

          // 处理行点击事件
          onRowClicked: function(row, filepath) {
            if (row === root.lastClickedRow) {
              // 点击同一行：如果当前没展开，则展开
              if (currentRow !== row || !root.isTableFocused) {
                currentRow = row
                docSelected(paginatedModel.docId(row))
                root.activedFileName = paginatedModel.data(paginatedModel.index(row, 0), 259)
              }
            } else {
              // 点击不同行：直接选中并展开
              currentRow = row
              docSelected(paginatedModel.docId(row))
              root.activedFileName = paginatedModel.data(paginatedModel.index(row, 0), 259)
            }
            root.lastClickedRow = row
            root.isTableFocused = true
          }
          onOrderChnage: {
            searchControlsModel.sort(role, !!order)
            paginatedModel.topHits = root.hits
          }

          onRightClickedRow: {
            const activedFilePaths = [];
            for(let i of tableView.selects.sort()) {
              activedFilePaths.push(paginatedModel.path(i));
            }
            root.activedFilePaths_ = activedFilePaths
            tableMenu.popup(parent, x, y)
          }

          onDbClickedRow: {
            skyFileMenu.openFiles([filepath])
          }

        // TODO)) 和Utils.COLUMN_INFO 一一对应
        QC1.TableViewColumn {
          role: "index"
          title: "序号"
          width: 47
          horizontalAlignment: Qt.AlignHCenter | Qt.AlignVCenter
        }
        QC1.TableViewColumn {
          role: "fileIcon"
          title: ""
          width: 30
          horizontalAlignment: Qt.AlignHCenter | Qt.AlignVCenter
          movable: false
        }
        QC1.TableViewColumn {
          role: "nameHighlighted"
          title: "名称"
          width: 200
          horizontalAlignment: Qt.AlignHCenter | Qt.AlignVCenter
        }
        QC1.TableViewColumn {
          role: "fullTextHighlighted"
          title: "摘要"
          width: 400
          visible: {
            return !!viewDeletedFileModel.fullTextQuery
          }
          horizontalAlignment: Qt.AlignHCenter | Qt.AlignVCenter
        }
        QC1.TableViewColumn {
          role: "size"
          title: "大小"
          width: 150
          horizontalAlignment: Qt.AlignHCenter | Qt.AlignVCenter
        }
        QC1.TableViewColumn {
          role: "extension"
          title: "扩展名"
          width: 150
          horizontalAlignment: Qt.AlignHCenter | Qt.AlignVCenter
        }
        QC1.TableViewColumn {
          role: "dirHighlighted"
          title: "所在目录"
          width: 200
          horizontalAlignment: Qt.AlignHCenter | Qt.AlignVCenter
        }
        QC1.TableViewColumn {
          role: "creationTime"
          title: "删除时间"
          width: 200
          horizontalAlignment: Qt.AlignHCenter | Qt.AlignVCenter
          visible: Qt.platform.os != "linux"
        }
        // QC1.TableViewColumn {
        //   role: "lastModificationTime"
        //   title: "最后修改时间"
        //   width: 200
        //   horizontalAlignment: Qt.AlignHCenter | Qt.AlignVCenter
        // }
        // QC1.TableViewColumn {
        //     role: "lastAccessTime"
        //     title: "最后访问时间"
        //     width: 200
        //     horizontalAlignment: Qt.AlignHCenter | Qt.AlignVCenter
        // }
        }

        // 分页控制栏
        Rectangle {
          id: paginationBar
          anchors.bottom: parent.bottom
          anchors.left: parent.left
          anchors.right: parent.right
          height: 45
          color: "transparent"
          // border.color: "#E0E0E0"
          // border.width: 1

          Row {
            anchors.right: parent.right
            anchors.rightMargin: 16
            anchors.verticalCenter: parent.verticalCenter
            spacing: 8

            SkyLabel {
              anchors.verticalCenter: parent.verticalCenter
              text: "共 " + totalItems + " 条，每页 " + pageSize + " 条"
              color: skyTheme.minorFontColor || "#666666"
              font.pixelSize: skyTheme.fontSize || 12
            }

            SkyButton {
              text: "首页"
              width: 50
              height: 28
              fontSize: skyTheme.fontSize || 11
              enabled: currentPage > 1
              onClicked: goToPage(1)
            }

            SkyButton {
              text: "上一页"
              width: 60
              height: 28
              fontSize: skyTheme.fontSize || 11
              enabled: currentPage > 1
              onClicked: goToPage(currentPage - 1)
            }

            SkyLabel {
              anchors.verticalCenter: parent.verticalCenter
              text: currentPage + " / " + Math.max(1, totalPages)
              color: skyTheme.mainFontColor || "#333333"
              font.pixelSize: skyTheme.fontSize || 12
            }

            SkyButton {
              text: "下一页"
              width: 60
              height: 28
              fontSize: skyTheme.fontSize || 11
              enabled: currentPage < totalPages
              onClicked: goToPage(currentPage + 1)
            }

            SkyButton {
              text: "末页"
              width: 50
              height: 28
              fontSize: skyTheme.fontSize || 11
              enabled: currentPage < totalPages
              onClicked: goToPage(totalPages)
            }
          }
        }
      }
    }

    // 空数据
    Item {
      Layout.fillWidth: true
      Layout.fillHeight: true
      SkyEmpty {
        anchors.fill: parent
        text: {
          if (viewDeletedFileModel.indexState == MainWindowViewModel.Init) {
            return "准备加载"
          }

          if (viewDeletedFileModel.indexState == MainWindowViewModel.Error) {
            return "加载失败"
          }

          if (viewDeletedFileModel.filterState === 1) {
            return "正在搜索..."
          }

          if (viewDeletedFileModel.filterState === 2) {
            return "正在停止搜索..."
          }

          if (viewDeletedFileModel.filterState === 3) {
            return "已停止搜索"
          }

          if (searchControlsModel.topHits && searchControlsModel.topHits.size == 0)
            return "没有搜索结果<br><br><font color=\"#666\" size=\"1\">本机可能没有符合搜索条件的文件<br>或者该文件在程序启动之后创建</font>"

          return ""
        }
        size: 220
        isLoading: {
          return viewDeletedFileModel.indexState == MainWindowViewModel.Loading && viewDeletedFileModel.searching
        }
      }
    }
  }
   
  
  // 右键菜单遮罩，用来阻止table表格滚动事件
  Rectangle {
    anchors.fill: parent
    visible: tableMenu.visible
    color: Qt.rgba(0, 0, 0, 0)
    MouseArea {
      anchors.fill: parent
      onWheel: { }
      onPressed: mouse.accepted = false;
      onReleased: mouse.accepted = false;
      onClicked: mouse.accepted = false;
    }
  }
  // 右键菜单
  SkyMenu {
    id: tableMenu
    focus: true
    SkyMenuItem {
      text: "打开"
      visible: {
        return activedFilePaths_.length == 1
      }
      iconSource: SkyIcons.OpenFile
      onTriggered: {
        skyFileMenu.openFiles(activedFilePaths_)
      }
    }
    SkyMenuItem {
      text: "打开所在目录"
      iconSource: SkyIcons.OpenLocal
      visible: {
        return activedFilePaths_.length == 1
      }
      onTriggered: {
        skyFileMenu.openFolder(activedFilePaths_[0])
      }
    }
    SkyMenuItem {
      text: "复制名称"
      iconSource: SkyIcons.Set
      onTriggered: {
        const isSuccess = skyFileMenu.copyFileNames(activedFilePaths_)
        if (isSuccess) {
          showSuccess("复制名称成功")
        }
      }
    }
    SkyMenuItem {
      text: "复制路径"
      iconSource: SkyIcons.DuplexPortraitOneSided
      onTriggered: {
        const isSuccess = skyFileMenu.copyFilePaths(activedFilePaths_)
        if (isSuccess) {
          showSuccess("复制路径成功")
        }
      }
    }
    SkyMenuItem {
      text: {
        if (disabled) {
          return "深度文件清理（高级版）"
        }
        return "深度文件清理"
      }
      iconSource: SkyIcons.ClearAllInkMirrored
      disabled: {
        return viewDeletedFileModel.license.level !== QLicense.Ultimate && !viewDeletedFileModel.skipLicense
      }
      onTriggered: {
        if (disabled) {
          showWarning("仅高级版支持该功能")
          return
        }
        skyFileMenu.cleanFiles(activedFilePaths_)
      }
    }
  }
}