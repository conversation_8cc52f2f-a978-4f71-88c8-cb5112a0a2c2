// TODO)) 和QC1.TableViewColumn 一一对应
let COLUMN_INFO = [
  {
    name: "index",
    sortName: "index",
    title: "序号",
    sortable: false,
    sortOrder: null,
    enableBorder: true,
  },
  {
    name: "fileIcon",
    sortName: "fileIcon",
    title: "",
    sortable: false,
    sortOrder: null,
    isImage: true,
    enableBorder: false,
  },
  {
    name: "nameHighlighted",
    sortName: "name",
    title: "名称",
    sortable: true,
    sortOrder: null,
    enableTooltip: false,
    enableBorder: true,
  },
  {
    name: "fullTextHighlighted",
    sortName: "fullText",
    title: "摘要",
    sortable: true,
    sortOrder: null,
    enableTooltip: true,
    enableBorder: true,
  },
  {
    name: "size",
    sortName: "size",
    title: "大小",
    sortable: true,
    sortOrder: null,
    hAlign: "center",
    enableBorder: true,
  },
  {
    name: "extension",
    sortName: "extension",
    title: "扩展名",
    sortable: true,
    sortOrder: null,
    hAlign: "center",
    enableBorder: true,
  },
  {
    name: "dirHighlighted",
    sortName: "dir",
    title: "所在目录",
    sortable: true,
    sortOrder: null,
    enableTooltip: true,
    enableBorder: true,
  },
  {
    name: "creationTime",
    sortName: "creationTime",
    title: "删除时间",
    sortable: true,
    sortOrder: null,
    hAlign: "center",
    enableBorder: true,
  },
  // {
  //   name: "lastModificationTime",
  //   sortName: "lastModificationTime",
  //   title: "最后修改时间",
  //   sortable: true,
  //   sortOrder: null,
  //   hAlign: "center",
  //   enableBorder: false,
  // },
  // {
  //   name: "lastAccessTime",
  //   sortName: "lastAccessTime",
  //   title: "最后访问时间",
  //   sortable: true,
  //   sortOrder: null,
  //   hAlign: "center"
  // },
];

function getColumnInfo(columnIndex) {
  return COLUMN_INFO[columnIndex];
}

function getColumnIndexByName(name) {
  return COLUMN_INFO.findIndex((info) => info.name === name);
}
