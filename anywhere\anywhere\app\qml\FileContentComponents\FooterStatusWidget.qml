import QtQuick 2.12
import QtQuick.Controls 2.12
import QtQuick.Layouts 1.15
import SkyUi 1.0
import AnywhereQmlModule 1.0


Rectangle {
  id: footControl
  anchors.fill: parent
  color: "transparent"
  
  property int pendingMaxSize: 0

  signal viewClicked()

  Rectangle {
    height: 1
    width: parent.width
    color: "#D1D1D1"
  }

  SkyProgress {
    z: 2
    y: 0 - height
    width: parent.width
    height: 4
    value: viewModel.indexProgress
    visible: {
      return false
      // return viewModel.indexState === MainWindowViewModel.Loading || viewModel.indexState === MainWindowViewModel.Error;
    }
  }

  Connections {
    target: viewModel
    function onPendingFilesSetChanged() {
      footControl.pendingMaxSize = viewModel.pendingFilesSet.count;
    }

    function onTopHitsChanged() {
    }
  }

  RowLayout {
    anchors.fill: parent
    spacing: 0

    Item {
      Layout.fillHeight: true
      Layout.preferredWidth: height
      Rectangle {
        anchors.fill: parent
        anchors.topMargin: 1
        color: "transparent"
        SkyIcon {
          id: stateIcon
          iconSource: {
            if (viewModel.pendingFilesSet && !!viewModel.pendingFilesSet.count && viewModel.filterState != 3) {
              return SkyIcons.Sync
            }
            if (viewModel.indexState === MainWindowViewModel.Success || viewModel.filterState === 3)
              return SkyIcons.Ringer
            if (viewModel.indexState === MainWindowViewModel.Error)
              return SkyIcons.SyncError
            if (!viewModel.searching)
              return SkyIcons.Ringer
            return SkyIcons.Sync
          }
          color: "#666"
          font.pixelSize: skyTheme.fontSizeLarge
          enabledClick: false
          anchors.centerIn: parent
          verticalAlignment: Text.AlignVCenter
        }
        RotationAnimation{
          id: rotationAnimation
          target: stateIcon
          to: 360
          from: 0
          direction: RotationAnimation.Clockwise
          running: (viewModel.indexState != MainWindowViewModel.Success&&viewModel.searching) && viewModel.indexState != MainWindowViewModel.Error || viewModel.pendingFilesSet && !!viewModel.pendingFilesSet.count && viewModel.filterState != 3
          duration: 800
          loops: Animation.Infinite
          onStopped: {
            stateIcon.rotation = 0
          }
        }
      }
      MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
          toolTip.visible = true
        }
        onExited: {
          toolTip.visible = false
        }
      }
      SkyTooltip {
        id: toolTip
        text: {
          if (viewModel.indexState === MainWindowViewModel.Error) return "读取文件列表失败： " + viewModel.indexErrorReason;
          return "文件总数" + (viewModel.indexedFilesCnt ? viewModel.indexedFilesCnt : "0") + "个"
        }
      }
    }

    Item {
      Layout.fillHeight: true
      Layout.fillWidth: true
      visible: (viewModel.topHits && viewModel.topHits.size) || viewModel.indexState === MainWindowViewModel.Success || !viewModel.searching

      SkyText {
        height: parent.height
        verticalAlignment: Text.AlignVCenter
        color: "#666"
        // anchors.centerIn: parent
        text: {
          if (viewModel.filterState === 2) return "正在停止搜索，请稍后..."
          if (viewModel.filterState === 3) return "已停止搜索"
          if ((viewModel.topHits && !viewModel.topHits.size && viewModel.searching)) return "正在搜索"
          if (viewModel.indexState === MainWindowViewModel.Loading) return "正在初始化，请稍后..."
          if (!viewModel.searching && !viewModel.topHits) return "已就绪"
          if (viewModel.pendingFilesSet && !viewModel.pendingFilesSet.count && !viewModel.searching) {
            var fileCount = viewModel.topHits ? viewModel.topHits.size : 0
            if (fileCount >= 200000) {
              return "搜索完成，搜索到200000+个文件，请优化搜索条件"
            }
            return "搜索完成, 搜索到" + fileCount + "个文件 "
          }
          return "正在搜索，搜索到" + (viewModel.topHits ? viewModel.topHits.size : "0") + "个文件"
        }
      }
    }

    Item {
      Layout.fillHeight: true
      Layout.fillWidth: true
      visible: viewModel.indexState === MainWindowViewModel.Loading && viewModel.searching

      SkyText {
        height: parent.height
        verticalAlignment: Text.AlignVCenter
        color: "#666"
        // anchors.centerIn: parent
        text: "正在读取文件列表，请稍后"
      }
    }

    // 计算距索引建立所用时间
    Timer {
      id: timer
      interval: 10000
      running: true
      repeat: true
      property int elapsedTime: 0
      property string elapsedStr: ""
      onTriggered: elapsedTime = viewModel.elapsedSecondsSinceIndexUpdated();
      onElapsedTimeChanged: {
        const elapsedMinute = Math.floor(elapsedTime / 60);
        const days = Math.floor(elapsedMinute / (60 * 24));
        const hours = Math.floor((elapsedMinute % (60 * 24)) / 60);
        const mins = elapsedMinute % 60;
        elapsedStr = (days > 0 ? days + "天":"") + (hours > 0 ? hours + "小时":"") + (mins > 0 ? mins + "分钟":"")
      }
    }

    // 展示距索引建立所用时间
    Item {
      id: time
      Layout.fillHeight: true
      Layout.preferredWidth: timeIcon.width + timeText.contentWidth
      Layout.rightMargin: 32
      Layout.leftMargin: 32
      visible: timer.elapsedTime && timer.elapsedStr

      Item {
        id: timeIcon
        height: parent.height
        width: height
        anchors.topMargin: 1
        anchors.top: parent.top
        SkyIcon {
          iconSource: SkyIcons.Stopwatch
          color: "#666"
          font.pixelSize: skyTheme.fontSizeLarge
          enabledClick: false
          anchors.centerIn: parent
          verticalAlignment: Text.AlignVCenter
        }
      }

      SkyText {
        id: timeText
        anchors.left: timeIcon.right
        height: parent.height
        verticalAlignment: Text.AlignVCenter
        color: "#666"
        text: timer.elapsedStr + "前"
      }

      MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
          timeToolTip.visible = true;
        }
        onExited: {
          timeToolTip.visible = false;
        }
      }
      
      SkyTooltip {
        id: timeToolTip
        text: {
          return "文件列表首次加载于" + timer.elapsedStr + "前"
        }
      }
    }

    Item {
      id: pendingProgress
      Layout.fillHeight: true
      Layout.preferredWidth: 100
      Layout.rightMargin: 32
      visible: viewModel.pendingFilesSet && viewModel.pendingFilesSet.count && footControl.pendingMaxSize
      
      SkyProgress {
        width: parent.width
        height: 2
        anchors.centerIn: parent
        value: {
          if (!viewModel.pendingFilesSet) return 0;
          return (1 - viewModel.pendingFilesSet.count / Math.max(viewModel.pendingFilesSet.count, footControl.pendingMaxSize))
        }
      }

      SkyTooltip {
        id: pendingTooltip
        visible: pendingProgress.visible
        text: {
          if (viewModel.filterState === 3) {
            return "已停止搜索"
          }
          if (viewModel.pendingFilesSet) {
            return "<b>后台搜索仍在进行中...</b><br>正在为您检索符合文件名条件的文件内容，剩余" + viewModel.pendingFilesSet.count + "个"
          }
          return "<b>后台搜索仍在进行中...</b>"
        }
      }
      
      MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered: {
          pendingTooltip.visible = true;
        }
        onExited: {
          pendingTooltip.visible = false;
        }
      }
    }
  }
}  