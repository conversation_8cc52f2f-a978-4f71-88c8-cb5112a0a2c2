import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Controls.Universal 2.12
import QtQuick.Layouts 1.15
import QtQuick.Controls 1.2 as QC1
import QtQuick.Controls.Styles 1.4 as QC1Styles
import AnywhereQmlModule 1.0
import SkyUi 1.0

Rectangle {
    property QDocSet docSet
    property int activeDocId
    signal docSelected(docId: int)

    onDocSetChanged: { pendingFilesTableModel.reset(docSet) }

    PendingFilesTableModel {
        id: pendingFilesTableModel
    }

    SkyTable {
        id: tableView
        anchors.fill: parent
        tableModel: pendingFilesTableModel
        
        onClickRow: {
            docSelected(pendingFilesTableModel.docId(row))
        }

        QC1.TableViewColumn {
            role: "name"
            title: "名称"
            width: 200
        }
        QC1.TableViewColumn {
            role: "sizeStr"
            title: "大小"
            width: 50
        }
        QC1.TableViewColumn {
            role: "extension"
            title: "扩展名"
            width: 50
        }
        QC1.TableViewColumn {
            role: "dir"
            title: "所在目录"
            width: 300
        }
        QC1.TableViewColumn {
            role: "creationTime"
            title: "创建时间"
            width: 200
        }
        QC1.TableViewColumn {
            role: "lastModificationTime"
            title: "最后修改时间"
            width: 200
        }
        QC1.TableViewColumn {
            role: "lastAccessTime"
            title: "最后访问时间"
            width: 200
        }
    }
}