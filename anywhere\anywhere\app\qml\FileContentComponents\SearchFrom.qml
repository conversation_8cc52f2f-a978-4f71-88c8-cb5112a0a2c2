import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Layouts 1.15
import QtQuick.Controls 1.4 as Controls1
import QtQuick.Dialogs 1.3
import SkyUi 1.0
import AnywhereQmlModule 1.0

SkyCard {
  property bool expanded: false
  property bool previewerEnabled: false
  property bool caseInsensive: true
  property bool searchFullPath: false
  property bool enableCompression: false
  property string name: ""
  property string fileNameQuery: ""
  property string fullTextQuery: ""
  property var filenamesHistory: []
  property var fullTextsHistory: []

  // 新增筛选条件属性
  property string createTimeFrom: ""
  property string createTimeTo: ""
  property string modifyTimeFrom: ""
  property string modifyTimeTo: ""
  property string fileSizeMin: ""
  property string fileSizeMax: ""
  property bool showAdvancedFilters: false

  anchors.fill: parent
  signal formChage(string fileName, string fullText, bool caseInsensive, bool searchOnlyFileName,
                   string createTimeFrom, string createTimeTo, string modifyTimeFrom, string modifyTimeTo,
                   string fileSizeMin, string fileSizeMax)
  signal setEnableCompression(bool enableCompression)
  signal previewShouldCollapse()  // 新增：通知需要折叠preview的信号

  backgroundValue: "transparent"
  paddingValue: 0
  marginValue: 0
  radiusValue: 0
  id: root

  // 类似QDateTimeEdit的日期选择器组件
  property string currentDateField: ""
  property var currentDatePicker: null

  Component {
    id: datePickerComponent
    Item {
      property string dateValue: ""
      property string placeholderText: "yyyy-MM-dd"
      property bool isOpen: false
      signal dateChanged(string newDate)

      id: datePickerRoot
      width: 100
      height: 25



      // 主输入框
      Rectangle {
        anchors.fill: parent
        border.color: datePickerRoot.isOpen ? "#1890FF" : "#d9d9d9"
        border.width: 1
        radius: 2
        color: "#ffffff"

        // 主输入区域
        Row {
          anchors.fill: parent
          anchors.margins: 1
          spacing: 0

          // 日期显示区域
          Rectangle {
            width: parent.width - 18
            height: parent.height
            color: "transparent"

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: datePickerRoot.dateValue || datePickerRoot.placeholderText
              color: datePickerRoot.dateValue ? "#333" : "#999"
              font.pixelSize: skyTheme.fontSize || 11
            }

            MouseArea {
              anchors.fill: parent
              onClicked: {
                datePickerRoot.isOpen = !datePickerRoot.isOpen
              }
            }
          }

          // 日历图标
          Rectangle {
            width: 18
            height: parent.height
            color: "transparent"

            Text {
              anchors.centerIn: parent
              text: "📅"
              font.pixelSize: 10
              color: "#666"
            }

            MouseArea {
              anchors.fill: parent
              onClicked: {
                datePickerRoot.isOpen = !datePickerRoot.isOpen
              }
            }
          }
        }
      }

      // 真正的日历面板
      Rectangle {
        id: calendarPanel
        visible: datePickerRoot.isOpen
        width: 280
        height: 320
        y: parent.height + 2
        x: Math.max(0, Math.min(parent.width - width, 0))
        color: "#ffffff"
        border.color: "#d9d9d9"
        border.width: 1
        radius: 4
        z: 1000

        // 阻止点击事件透传到下面的表格
        MouseArea {
          anchors.fill: parent
          onClicked: {
            // 阻止事件透传，不做任何操作
          }
        }

        property int currentYear: new Date().getFullYear()
        property int currentMonth: new Date().getMonth() + 1

        Column {
          anchors.fill: parent
          anchors.margins: 10
          spacing: 8

          // 年月导航
          Row {
            width: parent.width
            height: 30

            Rectangle {
              width: 30
              height: 30
              color: "transparent"

              Text {
                anchors.centerIn: parent
                text: "◀"
                font.pixelSize: 14
                color: "#666"
              }

              MouseArea {
                anchors.fill: parent
                onClicked: {
                  if (calendarPanel.currentMonth === 1) {
                    calendarPanel.currentMonth = 12
                    calendarPanel.currentYear--
                  } else {
                    calendarPanel.currentMonth--
                  }
                }
              }
            }

            Item {
              width: parent.width - 60
              height: 30

              Text {
                anchors.centerIn: parent
                text: calendarPanel.currentYear + "年" + calendarPanel.currentMonth + "月"
                font.pixelSize: 14
                font.bold: true
                color: "#333"
              }
            }

            Rectangle {
              width: 30
              height: 30
              color: "transparent"

              Text {
                anchors.centerIn: parent
                text: "▶"
                font.pixelSize: 14
                color: "#666"
              }

              MouseArea {
                anchors.fill: parent
                onClicked: {
                  if (calendarPanel.currentMonth === 12) {
                    calendarPanel.currentMonth = 1
                    calendarPanel.currentYear++
                  } else {
                    calendarPanel.currentMonth++
                  }
                }
              }
            }
          }

          // 星期标题
          Row {
            width: parent.width
            height: 25

            Repeater {
              model: ["日", "一", "二", "三", "四", "五", "六"]
              Rectangle {
                width: (parent.width) / 7
                height: 25
                color: "#f5f5f5"

                Text {
                  anchors.centerIn: parent
                  text: modelData
                  font.pixelSize: skyTheme.fontSize || 11
                  color: "#666"
                  font.bold: true
                }
              }
            }
          }

          // 日期网格
          Grid {
            width: parent.width
            height: 180
            columns: 7
            rows: 6

            Repeater {
              model: 42 // 6周 * 7天

              delegate: Rectangle {
                width: parent.width / 7
                height: 30

                property int dayNumber: {
                  var firstDay = new Date(calendarPanel.currentYear, calendarPanel.currentMonth - 1, 1)
                  var startDay = firstDay.getDay() // 0=Sunday, 1=Monday, etc.
                  return index - startDay + 1
                }

                property bool isValidDay: {
                  var daysInMonth = new Date(calendarPanel.currentYear, calendarPanel.currentMonth, 0).getDate()
                  return dayNumber >= 1 && dayNumber <= daysInMonth
                }

                property bool isToday: {
                  if (!isValidDay) return false
                  var today = new Date()
                  return dayNumber === today.getDate() &&
                         calendarPanel.currentMonth === (today.getMonth() + 1) &&
                         calendarPanel.currentYear === today.getFullYear()
                }

                property bool isSelected: {
                  if (!datePickerRoot.dateValue || !isValidDay) return false
                  try {
                    var parts = datePickerRoot.dateValue.split("-")
                    if (parts.length !== 3) return false
                    var year = parseInt(parts[0])
                    var month = parseInt(parts[1])
                    var day = parseInt(parts[2])
                    if (isNaN(year) || isNaN(month) || isNaN(day)) return false
                    return year === calendarPanel.currentYear &&
                           month === calendarPanel.currentMonth &&
                           day === dayNumber
                  } catch (e) {
                    console.warn("日期解析错误:", e)
                    return false
                  }
                }

                color: {
                  if (!isValidDay) return "transparent"
                  if (isToday) return "#1890FF"
                  if (isSelected) return "#bae7ff"
                  if (dayMouseArea.containsMouse) return "#e6f7ff"
                  return "transparent"
                }

                border.color: isToday ? "#ffffff" : "transparent"
                border.width: isToday ? 1 : 0
                radius: 2

                Text {
                  anchors.centerIn: parent
                  text: parent.isValidDay ? parent.dayNumber : ""
                  font.pixelSize: skyTheme.fontSize || 11
                  color: {
                    if (!parent.isValidDay) return "transparent"
                    if (parent.isToday) return "white"
                    if (parent.isSelected) return "#1890FF"
                    return "#333"
                  }
                  font.bold: parent.isToday || parent.isSelected
                }

                MouseArea {
                  id: dayMouseArea
                  anchors.fill: parent
                  hoverEnabled: true
                  enabled: parent.isValidDay
                  cursorShape: enabled ? Qt.PointingHandCursor : Qt.ArrowCursor

                  onClicked: {
                    try {
                      var month = calendarPanel.currentMonth < 10 ? "0" + calendarPanel.currentMonth : calendarPanel.currentMonth.toString()
                      var day = parent.dayNumber < 10 ? "0" + parent.dayNumber : parent.dayNumber.toString()
                      var dateStr = calendarPanel.currentYear + "-" + month + "-" + day
                      datePickerRoot.dateValue = dateStr
                      datePickerRoot.dateChanged(dateStr)
                      datePickerRoot.isOpen = false
                    } catch (e) {
                      console.warn("日期点击处理错误:", e)
                    }
                  }
                }
              }
            }
          }

          // 底部按钮
          Row {
            anchors.horizontalCenter: parent.horizontalCenter
            spacing: 10

            Rectangle {
              width: 60
              height: 25
              color: "#52c41a"
              radius: 2

              Text {
                anchors.centerIn: parent
                text: "今天"
                color: "white"
                font.pixelSize: skyTheme.fontSize || 11
              }

              MouseArea {
                anchors.fill: parent
                onClicked: {
                  try {
                    var today = new Date()
                    var year = today.getFullYear()
                    var month = today.getMonth() + 1
                    var day = today.getDate()
                    var dateStr = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day)

                    calendarPanel.currentYear = year
                    calendarPanel.currentMonth = month
                    datePickerRoot.dateValue = dateStr
                    datePickerRoot.dateChanged(dateStr)
                    datePickerRoot.isOpen = false
                  } catch (e) {
                    console.warn("今天按钮处理错误:", e)
                  }
                }
              }
            }

            Rectangle {
              width: 60
              height: 25
              color: "#ff4d4f"
              radius: 2

              Text {
                anchors.centerIn: parent
                text: "清空"
                color: "white"
                font.pixelSize: skyTheme.fontSize || 11
              }

              MouseArea {
                anchors.fill: parent
                onClicked: {
                  datePickerRoot.dateValue = ""
                  datePickerRoot.dateChanged("")
                  datePickerRoot.isOpen = false
                }
              }
            }

            Rectangle {
              width: 60
              height: 25
              color: "#1890FF"
              radius: 2

              Text {
                anchors.centerIn: parent
                text: "取消"
                color: "white"
                font.pixelSize: skyTheme.fontSize || 11
              }

              MouseArea {
                anchors.fill: parent
                onClicked: {
                  datePickerRoot.isOpen = false
                }
              }
            }
          }
        }

        // 获取月份天数的函数
        function getDaysInMonth(year, month) {
          return new Date(year, month, 0).getDate()
        }
      }


    }
  }

  onFileNameQueryChanged: {
    if (fileName.fileNameQuery == fileNameQuery) return;
    fileName.value = fileNameQuery;
  }

  content: Item{
    anchors.fill: parent

    RowLayout {
      id: fileNameRow
      x: {
        return 0
      }
      y: {
        return 0
      }
      width: {
        if (previewerEnabled) {
          return parent.width - searchButton.width - advancedFilterButton.width - 50  // 恢复为50
        } else {
          return (parent.width - searchButton.width - advancedFilterButton.width) / 2 - 50  // 恢复为50
        }
      }
      height: {
        if (previewerEnabled) {
          return 30
        } else {
          return parent.height
        }
      }
      SkyTitle {
        text: qsTr("文件名称: ")
        Layout.preferredWidth: 80  // 增加宽度，确保与创建时间一致
        Layout.alignment: Qt.AlignVCenter | Qt.AlignRight
        Layout.fillHeight: true
        description: qsTr("多个关键词之间用<font color=\"red\">空格</font>分割，表示或的关系，比如：<font color=\"red\">秘密 机密 绝密</font><br>可以在关键词前加<font color=\"red\">+</font>或<font color=\"red\">-</font>表示必须包含或者不得包含关键词，如：<font color=\"red\">财务 绩效 考核 +内部文件</font><br>表示结果中<font color=\"red\">包含财务或绩效或考核，同时必须包含内部文件</font>")
      }
      SkyInpuSelect {
        id: fileName
        Layout.fillWidth: true
        implicitHeight: parent.height
        placeholderText: "在此输入文件名搜索条件"
        contentDescription: "多个关键词之间用<font color=\"red\">空格</font>分割，表示或的关系，比如：<font color=\"red\">秘密 机密 绝密</font>"
        prefixOptions: {
          return viewModel.roots
        }
        prefixDescription: {
          return "当前可搜索" + viewModel.roots.join(",") + "盘上的文件"
        }
        skyRightMeun: Component {
          id: fileNameRightMeun
          RowLayout {
            width: 50
            height: 25
            spacing: 0
            Item {
              Layout.fillWidth: true
              Layout.fillHeight: true
              
              SkyButton {
                anchors.fill: parent
                iconSize: skyTheme.fontSize
                type: "text"
                shape: "circle"
                enabledHovered: false
                iconSource: SkyIcons.Font
                showDisabledIcon: root.caseInsensive
                contentDescription: {
                  if (root.caseInsensive) {
                    return "未开启区分大小写"
                  } else {
                    return "已开启区分大小写"
                  }
                }
                skyIconColor: {
                  if (root.caseInsensive)
                    return "#333"
                  else
                    return "#1890FF"
                }
                onClicked: {
                  root.caseInsensive = !root.caseInsensive
                }
              }
            }
            Item {
              Layout.fillWidth: true
              Layout.fillHeight: true
              
              SkyButton {
                anchors.fill: parent
                iconSize: skyTheme.fontSize
                type: "text"
                shape: "circle"
                enabledHovered: false
                iconSource: SkyIcons.OpenFolderHorizontal
                showDisabledIcon: !root.searchFullPath
                contentDescription: {
                  if (root.searchFullPath) {
                    return "已开启检索全路径"
                  } else {
                    return "未开启检索全路径（当前仅检索文件名）"
                  }
                }
                skyIconColor: {
                  if (root.searchFullPath)
                    return "#1890FF"
                  else
                    return "#333"
                }
                onClicked: {
                  root.searchFullPath = !root.searchFullPath
                }
              }
            }
          }
        }
        skyOptions: [
          {
            title: (formatTranslation(fileName.value)),
            disabled: true
          },
          {
            title: "常用文档(.doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf)",
            value: ".doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf",
            group: "常用词",
            disabled: false
          },
          {
            title: "常用文档及压缩包(.doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf .7z .rar .zip)",
            value: ".doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf .7z .rar .zip",
            group: "常用词",
            disabled: false
          },
          {
            title: "常用图片(.png .jpg .tif .bmp)",
            value: ".png .jpg .tif .bmp",
            group: "常用词",
            disabled: false
          },
          {
            title: "秘密 机密 绝密",
            group: "常用词",
            disabled: false
          }
        ].concat(root.filenamesHistory)

        onSkyTabPressed: {
          fullText.forceActiveFocus()
        }
        // @@@@liukai 文件名输入框回车触发文件内容检查
        onValueChanged: {
          if (root.fileNameQuery == value) return;
          root.fileNameQuery = value;
        }

        Keys.onEnterPressed: {
          if (viewModel.searching) return;
          // 右 enter
          root.formChage(fileName.value, fullText.value, root.caseInsensive, root.searchFullPath,
                         root.createTimeFrom, root.createTimeTo, root.modifyTimeFrom, root.modifyTimeTo,
                         root.fileSizeMin, root.fileSizeMax)
          closePopup()
        }

        Keys.onReturnPressed: {
          if (viewModel.searching) return;
          // 左 enter
          root.formChage(fileName.value, fullText.value, root.caseInsensive, root.searchFullPath,
                         root.createTimeFrom, root.createTimeTo, root.modifyTimeFrom, root.modifyTimeTo,
                         root.fileSizeMin, root.fileSizeMax)
          closePopup()
        }
      }
    }

    RowLayout{
      id: fullTextRow
      x: {
        if (previewerEnabled) {
          return 0
        } else {
          return fileNameRow.width + 20
        }
      }
      y: {
        if (previewerEnabled) {
          return fileNameRow.height + 11
        } else {
          return 0
        }
      }
      width: {
        if (previewerEnabled) {
          return parent.width - searchButton.width - advancedFilterButton.width - 50  // 调整为50
        } else {
          return (parent.width - searchButton.width - advancedFilterButton.width) / 2 - 50  // 调整为50
        }
      }
      height: {
        if (previewerEnabled) {
          return 30
        } else {
          return parent.height
        }
      }
      SkyTitle {
        text: qsTr("文件内容: ")
        Layout.preferredWidth: 80
        Layout.alignment: Qt.AlignVCenter | Qt.AlignRight
        Layout.fillHeight: true
        description: qsTr("多个关键词之间用<font color=\"red\">空格</font>分割，表示或的关系，比如：<font color=\"red\">秘密 机密 绝密</font><br>可以在关键词前加<font color=\"red\">+</font>或<font color=\"red\">-</font>表示必须包含或者不得包含关键词，如：<font color=\"red\">财务 绩效 考核 +内部文件</font><br>表示结果中<font color=\"red\">包含财务或绩效或考核，同时必须包含内部文件</font>")
      }
      SkyInpuSelect {
        id: fullText
        Layout.preferredWidth: fileName.width
        // Layout.fillWidth: true
        implicitHeight: parent.height
        placeholderText: "在此输入文件内容搜索条件"
        contentDescription: "多个关键词之间用<font color=\"red\">空格</font>分割，表示或的关系，比如：<font color=\"red\">秘密 机密 绝密</font>"
        skyOptions: [
          {
            title: (formatTranslation(fullText.value)),
            disabled: true
          },
          {
            title: "秘密 机密 绝密",
            group: "常用词",
            disabled: false
          },
          {
            title: "机密 绝密",
            group: "常用词",
            disabled: false
          },
          {
            title: "绝密",
            group: "常用词",
            disabled: false
          }
        ].concat(root.fullTextsHistory)
        skyRightMeun: Component {
          id: fullTextRightMeun
          RowLayout {
            width: 25
            height: 25
            spacing: 0
            Item {
              Layout.fillWidth: true
              Layout.fillHeight: true
              
              SkyButton {
                anchors.fill: parent
                iconSize: skyTheme.fontSize
                type: "text"
                shape: "circle"
                enabledHovered: false
                iconSource: SkyIcons.Favicon2
                showDisabledIcon: !root.enableCompression
                contentDescription: {
                  if (root.enableCompression) {
                    return "已开启压缩包内容提取"
                  } else {
                    return "未开启压缩包内容提取"
                  }
                }
                skyIconColor: {
                  if (root.enableCompression)
                    return "#1890FF"
                  else
                    return "#333"
                }
                onClicked: {
                  const value = !root.enableCompression
                  if (value) {
                    showWarning("开启压缩包检索会降低检索速度")
                  }
                  root.setEnableCompression(value)
                }
              }
            }
          }
        }

        onSkyTabPressed: {
          fileName.forceActiveFocus()
        }
        // @@@@liukai 内容输入框回车触发文件内容检查
        Keys.onEnterPressed: {
          if (viewModel.searching) return;
          // 右 enter
          root.formChage(fileName.value, fullText.value, root.caseInsensive, root.searchFullPath,
                         root.createTimeFrom, root.createTimeTo, root.modifyTimeFrom, root.modifyTimeTo,
                         root.fileSizeMin, root.fileSizeMax)
          closePopup()
        }

        Keys.onReturnPressed: {
          if (viewModel.searching) return;
          // 左 enter
          root.formChage(fileName.value, fullText.value, root.caseInsensive, root.searchFullPath,
                         root.createTimeFrom, root.createTimeTo, root.modifyTimeFrom, root.modifyTimeTo,
                         root.fileSizeMin, root.fileSizeMax)
          closePopup()
        }
      }
    }

    // 高级筛选展开按钮
    SkyButton {
      id: advancedFilterButton
      width: 70  // 增加宽度
      x: {
        if(previewerEnabled) {
          return 10 + fileNameRow.width + 20  // 10是fileNameRow的x位置
        } else {
          return 10 + fileNameRow.width + fullTextRow.width + 40  // 10是fileNameRow的x位置
        }
      }
      y: {
        if(previewerEnabled) {
          return fileNameRow.height + 11
        } else {
          return 0
        }
      }
      height: {
        if(previewerEnabled) {
          return 30
        } else {
          return parent.height
        }
      }
      type: "text"
      text: "高级"
      iconSource: root.showAdvancedFilters ? SkyIcons.Up : SkyIcons.Down
      skyIconColor: "#1890FF"  // 蓝色箭头
      fontColor: "#1890FF"     // 蓝色文字
      contentDescription: root.showAdvancedFilters ? "收起高级筛选" : "展开高级筛选"
      onClicked: {
        root.showAdvancedFilters = !root.showAdvancedFilters
      }
    }

    SkyButton {
      id: searchButton
      width: 62
      x: {
        if(previewerEnabled) {
          return 10 + fileNameRow.width + advancedFilterButton.width + 35  // 10是fileNameRow的x位置
        } else {
          return 10 + fileNameRow.width + fullTextRow.width + advancedFilterButton.width + 55  // 10是fileNameRow的x位置
        }
      }
      y: {
        if(previewerEnabled) {
          return fileNameRow.height + 11
        } else {
          return 0
        }
      }
      height: {
        if(previewerEnabled) {
          return 30
        } else {
          return parent.height
        }
      }
      type: "primary"
      disabled: viewModel.indexState === MainWindowViewModel.Loading || (viewModel.searching &&  viewModel.filterState ===1) || viewModel.filterState === 2
      text: {
        if (viewModel.indexState === MainWindowViewModel.Loading && viewModel.searching) return "载入中";
        if (viewModel.filterState === 1 || viewModel.filterState === 2) return "停止"
        return "搜索"
      }
      // @@@@liukai 搜索按钮触发文件内容检查
      onClicked: {
        if (viewModel.filterState === 1 || viewModel.filterState === 2) {
          // 停止过滤时也需要折叠preview
          root.previewShouldCollapse()
          viewModel.endBackgroundIndexFileContentFiltering()
        } else {
          root.formChage(fileName.value, fullText.value, root.caseInsensive, root.searchFullPath,
                         root.createTimeFrom, root.createTimeTo, root.modifyTimeFrom, root.modifyTimeTo,
                         root.fileSizeMin, root.fileSizeMax)
        }
        forceActiveFocus()
      }
    }

    // 高级筛选区域 - 改为单行布局，不浮在表格上
    Rectangle {
      id: advancedFiltersArea
      visible: root.showAdvancedFilters
      width: parent.width
      height: visible ? 90 : 0  // 进一步增加高度，避免下边框被压线
      y: {
        if(previewerEnabled) {
          return fullTextRow.y + fullTextRow.height + 10
        } else {
          return parent.height + 10
        }
      }
      color: "#f8f9fa"  // 浅灰色背景
      border.color: "#1890ff"  // 蓝色边框
      border.width: 1
      radius: 6  // 圆角

      // 多行布局：第一行创建时间、修改时间，第二行文件大小
      ColumnLayout {
        anchors.fill: parent
        anchors.leftMargin: 15    // 增加左边距，与边框保持距离
        anchors.topMargin: 10
        anchors.rightMargin: 10
        anchors.bottomMargin: 15  // 增加底部边距，避免与下边框压线
        spacing: 10

        // 第一行：创建时间、修改时间
        RowLayout {
          spacing: 15
        RowLayout {
          spacing: 5
          // 创建时间标签
          SkyTitle {
            text: qsTr("创建时间:")
            Layout.preferredWidth: Math.max(60, implicitWidth + 10)
            font.pixelSize: skyTheme.fontSize || 11
            description: qsTr("筛选指定创建时间范围内的文件")
          }

          Loader {
            id: createTimeFromLoader
            sourceComponent: datePickerComponent
            Layout.preferredWidth: 100
            Layout.preferredHeight: 30
            onLoaded: {
              if (item) {
                try {
                  item.dateValue = Qt.binding(function() { return root.createTimeFrom || "" })
                  item.placeholderText = "开始日期"
                  item.dateChanged.connect(function(newDate) {
                    root.createTimeFrom = newDate || ""
                  })
                } catch (e) {
                  console.warn("创建时间From加载错误:", e)
                }
              }
            }
          }

          SkyTitle {
            text: qsTr("至")
            Layout.preferredWidth: 15
            font.pixelSize: skyTheme.fontSize || 11
          }

          Loader {
            id: createTimeToLoader
            sourceComponent: datePickerComponent
            Layout.preferredWidth: 100
            Layout.preferredHeight: 30
            onLoaded: {
              if (item) {
                try {
                  item.dateValue = Qt.binding(function() { return root.createTimeTo || "" })
                  item.placeholderText = "结束日期"
                  item.dateChanged.connect(function(newDate) {
                    root.createTimeTo = newDate || ""
                  })
                } catch (e) {
                  console.warn("创建时间To加载错误:", e)
                }
              }
            }
          }
        }
        // 分隔线
        Rectangle {
          Layout.preferredWidth: 1
          Layout.fillHeight: true
          color: "#e9ecef"
        }

        // 修改时间区域
        RowLayout {
          spacing: 5

          SkyTitle {
            text: qsTr("修改时间:")
            Layout.preferredWidth: Math.max(60, implicitWidth + 10)
            font.pixelSize: skyTheme.fontSize || 11
            description: qsTr("筛选指定修改时间范围内的文件")
          }

          Loader {
            id: modifyTimeFromLoader
            sourceComponent: datePickerComponent
            Layout.preferredWidth: 100
            Layout.preferredHeight: 30
            onLoaded: {
              if (item) {
                try {
                  item.dateValue = Qt.binding(function() { return root.modifyTimeFrom || "" })
                  item.placeholderText = "开始日期"
                  item.dateChanged.connect(function(newDate) {
                    root.modifyTimeFrom = newDate || ""
                  })
                } catch (e) {
                  console.warn("修改时间From加载错误:", e)
                }
              }
            }
          }

          SkyTitle {
            text: qsTr("至")
            Layout.preferredWidth: 15
            font.pixelSize: skyTheme.fontSize || 11
          }

          Loader {
            id: modifyTimeToLoader
            sourceComponent: datePickerComponent
            Layout.preferredWidth: 100
            Layout.preferredHeight: 30
            onLoaded: {
              if (item) {
                try {
                  item.dateValue = Qt.binding(function() { return root.modifyTimeTo || "" })
                  item.placeholderText = "结束日期"
                  item.dateChanged.connect(function(newDate) {
                    root.modifyTimeTo = newDate || ""
                  })
                } catch (e) {
                  console.warn("修改时间To加载错误:", e)
                }
              }
            }
          }
        }
        }

        // 第二行：文件大小
        RowLayout {
          spacing: 5

          SkyTitle {
            text: qsTr("文件大小:")
            Layout.preferredWidth: Math.max(60, implicitWidth + 10)
            font.pixelSize: skyTheme.fontSize || 11
            description: qsTr("筛选指定大小范围内的文件\n支持单位：B、KB、MB、GB、TB\n例如：1024、1.5MB、2GB")
          }

          Rectangle {
            Layout.preferredWidth: 120  // 增加宽度从80到120
            Layout.preferredHeight: 30  // 与主搜索框高度一致
            border.color: "#ccc"
            border.width: 1
            radius: 2

            TextInput {
              id: fileSizeMinInput
              anchors.fill: parent
              anchors.margins: 5
              verticalAlignment: TextInput.AlignVCenter
              font.pixelSize: skyTheme.fontSize || 11
              text: root.fileSizeMin
              onTextChanged: {
                console.log("文件大小最小值输入:", text)
                root.fileSizeMin = text
              }

              // 明确允许输入数字、字母、小数点和空格
              inputMethodHints: Qt.ImhNone
              selectByMouse: true

              // 调试：监听按键事件
              Keys.onPressed: {
                console.log("按键按下:", event.key, "文本:", event.text)
                event.accepted = false
              }

              // 调试：监听输入法事件
              onInputMethodComposingChanged: {
                console.log("输入法组合状态:", inputMethodComposing)
              }
            }

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: "最小值(如1MB)"
              color: "#999"
              font.pixelSize: skyTheme.fontSize || 10
              visible: fileSizeMinInput.text.length === 0
            }
          }

          SkyTitle {
            text: qsTr("至")
            Layout.preferredWidth: 15
            font.pixelSize: skyTheme.fontSize || 11
          }

          Rectangle {
            Layout.preferredWidth: 120  // 增加宽度从80到120
            Layout.preferredHeight: 30
            border.color: "#ccc"
            border.width: 1
            radius: 2

            TextInput {
              id: fileSizeMaxInput
              anchors.fill: parent
              anchors.margins: 5
              verticalAlignment: TextInput.AlignVCenter
              font.pixelSize: skyTheme.fontSize || 11
              text: root.fileSizeMax
              onTextChanged: {
                console.log("文件大小最大值输入:", text)
                root.fileSizeMax = text
              }

              // 明确允许输入数字、字母、小数点和空格
              inputMethodHints: Qt.ImhNone
              selectByMouse: true

              // 调试：监听按键事件
              Keys.onPressed: {
                console.log("按键按下:", event.key, "文本:", event.text)
                event.accepted = false
              }

              // 调试：监听输入法事件
              onInputMethodComposingChanged: {
                console.log("输入法组合状态:", inputMethodComposing)
              }
            }

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: "最大值(如10GB)"
              color: "#999"
              font.pixelSize: skyTheme.fontSize || 10
              visible: fileSizeMaxInput.text.length === 0
            }
          }
        }

        // 填充剩余空间
        Item { Layout.fillWidth: true }
      }
    }
  }

  function formatTranslation(value) {
    if (!value) return "<font size=\"12px\" color=\"#666\" >多个关键词之间用<font color=\"red\">空格</font>分割，表示或的关系，比如：<font color=\"red\">秘密 机密 绝密</font></font>"
    if (/[\*,\?]/.test(value)) return "<font color=\"red\" size=\"12px\">不支持通配符</font></font>"
    const arr = value.split(" ");
    let result = "<font color=\"#666\" size=\"12px\">"
    const forceStr = arr.filter(v => v[0] === "+").map(v => v.substr(1)).filter(v => !!v).join(" 和 ")
    const includeStr = arr.filter(v => v[0] !== "+" && v[0] !== "-").filter(v => !!v).join(" 或 ")
    const excludeArr = arr.filter(v => v[0] === "-").map(v => v.substr(1)).filter(v => !!v).join(" 和 ")
    if (!!forceStr) {
      result += "必须包含" + forceStr
    }

    if (!!includeStr) {
      if (!!forceStr) result += "，"
      result += "包含" + includeStr
    }

    if (!!excludeArr) {
      if (!!forceStr || !!includeStr) result += "，"
      result += "不得包含" + excludeArr
    }

    result += "</font>";
    return result
  }
}