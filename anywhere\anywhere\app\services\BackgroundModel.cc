#include "BackgroundModel.h"
#include <cassert>
#include <fmt/core.h>
#include <fmt/format.h>
#include <QThread>
#include <QFile>
#include <QCryptographicHash>
#include "CancellationToken.h"
#include "Executors.h"
#include "Miscs.h"
#include "core/tasks/IndexFileContentTask.h"
#include "core/ServiceRegistry.h"
#include "../utils/Executors.h"
#include "core/tasks/WaitForLoadIndexDoneTask.h"
#include "parser/DefaultFileParseEngine.h"
#include "TextNormalizer.h"
#include <QStandardPaths>
#include "ViolationProcessor.h"
#include <QDir>
#include "FileExtFilter.h"
#include "FileTypeManager.h"
#include "parser/FileParser.h"
#include "FileExtCheckerImpl.h"
#include "CancellationToken.h"
#include "recover/recover.h"
#include "CheckerUtils.h"
#include "clean/DefaultFileCleanEngineFactory.h"
#include "core/services/PrivilegedClientService.h"
using namespace anywhere::core;

namespace anywhere {
namespace app {
class BackgroundDocProcessor : public core::DocProcessor {
  public:
  BackgroundDocProcessor(BackgroundModel* model) : model_(model) {

  }
  void onDocIndexed(DocId id, const std::filesystem::path &path, const std::u8string &text,
    std::optional<SearchEngineService::Document> doc,
    void *context) {
      model_->onDocIndexed(id, path, text, doc, context);
      model_->addPathProcessed(path.string());
    }
  bool filterFile(const std::filesystem::path &path) {
    auto ret =  model_->filterFile(path);
    if (ret) {
      if (model_->checkPathProcessed(path.string())) {
        return false;
      }
    }
    return ret;
  }
  bool filterDoc(const SearchEngineService::Document&doc) {
    if (!model_->filterDoc(doc)) {
      return false;
    }
    auto path = doc.name();
    if (!filterFile(path)) {
      return false;
    }
    return true;
  }
  bool RecoverFile(uint64_t inode, int indexId, int volumId, const std::string& path) {
    return Recover::RecoverFile(inode, indexId, volumId, path);
  }
  std::string ConstructNewFileName(const std::string &filename, uint64_t inode) {
    auto name = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) +"/tmp/" +
      QString::fromStdString(model_->getTaskId()) + "_" + QString::number(inode) + "_" +
      QString::fromLocal8Bit(filename.c_str());
    return name.toLocal8Bit().constData();
  }
private:
  BackgroundModel *model_;
};

// class DeletedFileSaver : public ResultSaver {
// public:
//   DeletedFileSaver(std::shared_ptr<DeletedFilesTable> deletedFilesTable) : deletedFilesTable_(deletedFilesTable) {
//   }
//   bool Save(uint64_t inode, const std::string& path, int size, const std::string& deleteTime, int percent, int indexId, int volumId) {
//     std::tm tm = {};
//     std::istringstream ss(deleteTime);
//     ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
//     auto d = std::chrono::system_clock::from_time_t(std::mktime(&tm));
//     return deletedFilesTable_->insertOrUpdate(inode, path, size, d, percent, 0, indexId, volumId);
//   }
// private:
//   std::shared_ptr<DeletedFilesTable> deletedFilesTable_;
// };
void BackgroundModel::clean(const std::filesystem::path &fileName) {
  auto engine = fileCleanEngineFactory_->create(privilegedClientService_);
  engine->clean(fileName);
}

BackgroundModel::BackgroundModel(const std::string& taskId) :task_id_(taskId), db_((QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/system.db").toStdString()),
taskResultTable_(db_), violation_(false),uploadFileTable_(db_) {
  // 从ServiceRegistry获取依赖服务
  db_path_ = nullptr;
  pathTable_ = nullptr;
  configService_ = std::make_shared<core::ConfigService>();
  configService_->clone(ServiceRegistry::instance()->getService<ConfigServiceToken>().get());
  auto eventBusService = ServiceRegistry::instance()->getService<EventBusServiceToken>();
  std::filesystem::path currentLibPath = std::filesystem::path();
  /*if (!configService->isPortableVersion()) {
    currentLibPath = std::filesystem::current_path() / u8"lib";
  }*/
  auto tmpPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) +"/tmp/";
  QDir dir(tmpPath);
  if (!dir.exists()) {
    if (!dir.mkpath(tmpPath)) {
      qWarning() << "Failed to create directory:" << dir.absolutePath();
    }
  }
  
  auto tmpDir = std::filesystem::temp_directory_path();
  auto 
    resourceReader = ServiceRegistry::instance()->getService<ResourceReaderToken>();
  auto defaultFileParseEngine =
      std::make_shared<parser::DefaultFileParseEngine>(
          tmpDir, currentLibPath, resourceReader,
          configService_->enabledFileFormats(),
          configService_->maxCharactorsPerFile());
  defaultFileParseEngine->setFileExtChecker(std::make_shared<FileExtCheckerImpl>());
  fileFormatDetector_ = std::make_shared<parser::DefaultFileFormatDetector>(tmpDir, resourceReader);
  // auto defaultFileParseEngine = ServiceRegistry::instance()->getService<DefaultFileParseEngineToken>();
  licenseService_ = ServiceRegistry::instance()->getService<LicenseServiceToken>();
  // mainThreadExecutor_ = ServiceRegistry::instance()->getService<MainExecutorToken>();
  ioTheadExecutor_ = ServiceRegistry::instance()->getService<IoExecutorToken>();
  mainThreadExecutor_ = ioTheadExecutor_;
  queryParser_ = ServiceRegistry::instance()->getService<QueryParserToken>();
  fileIconProvider_ =
      ServiceRegistry::instance()->getService<FileIconProviderToken>();
  highlighterFactory_ =
      ServiceRegistry::instance()->getService<HighlighterFactoryToken>();
  // 创建独立的搜索服务实例
  searchEngineService_ = std::make_shared<core::SearchEngineService>(
    configService_,
      eventBusService,
      mainThreadExecutor_,
      ioTheadExecutor_
  );

  // 创建独立的索引服务实例
  fileContentIndexService_ = std::make_shared<core::FileContentIndexService>(
    configService_,
      searchEngineService_,
      defaultFileParseEngine,
      eventBusService,
      mainThreadExecutor_,
      ioTheadExecutor_
  );

  searchEngineService_->toBackground();
  fileContentIndexService_->setDocProcessor(std::make_shared<BackgroundDocProcessor>(this));
  fileCleanEngineFactory_ = std::make_shared<clean::DefaultFileCleanEngineFactory>();
  privilegedClientService_ = ServiceRegistry::instance()->getService<PrivilegedClientServiceToken>();
}

BackgroundModel::~BackgroundModel() { close(); 
  if (pathTable_ != nullptr) delete pathTable_;
  if (db_path_ != nullptr)
    delete db_path_;
  if (!tmpDBPath_.isEmpty()) {
    QDir dir(tmpDBPath_);
    if (dir.exists()) {
      dir.removeRecursively();
    }
    tmpDBPath_ = "";
  }
}

void BackgroundModel::close() {
  searchTaskCancallationSource_.requestCancellation();
  backgroundIndexFileContentTaskCancallationSource_.requestCancellation();
  miscTaskCancellationSource_.requestCancellation();
}

void BackgroundModel::search(const QString &fileNameQuery,
                             const QString &fileNameExcludeQuery,
                             const QString &fullTextQuery,
                             const QString &fullTextExcludeQuery,
                             const QString &sortProperty, bool sortOrderAsc,
                             bool caseInsensive, bool searchFullPath,
                             bool enableCompression, int summaryCount,
                             int summaryLength) {
  auto dbname = QString::fromStdString(task_id_) + "_ntfs.db";
  tmpDBPath_ =
      QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) +
      "/tmp/";
  db_path_ = new Database((tmpDBPath_ + dbname).toStdString());
  pathTable_ = new PathTable(*db_path_);

  // dbname = QString::fromStdString(task_id_) + "_" + timeEpoch_ + "_del.db";
  // db_delete_files_ = new Database((tmpDBPath_ + dbname).toStdString());
  // deletedFilesTable_.reset(new DeletedFilesTable(*db_delete_files_));
  fileNameQuery_ = fileNameQuery;
  fullTextQuery_ = fullTextQuery;
  fullTextExcludeQuery_ = fullTextExcludeQuery;
  fileNameExcludeQuery_ = fileNameExcludeQuery;
  // searchTaskCancallationSource_.requestCancellation();
  searchTaskCancallationSource_ = CancellationSource();

  // 使用buildSearchComponents构建搜索组件
  auto searchComponents = buildSearchComponents(
      fileNameQuery, fileNameExcludeQuery, fullTextQuery, fullTextExcludeQuery,
      caseInsensive, summaryLength, summaryCount);

  // 设置成员变量（直接使用buildSearchComponents的结果）
  fileNameTermsMatcher_ = std::move(searchComponents.fileNameMatcher);
  fileNameTermsExcludeMatcher_ =
      std::move(searchComponents.fileNameExcludeMatcher);
  fullTextTermsMatcher_ = std::move(searchComponents.fullTextMatcher);
  fullTextTermsExcludeMatcher_ =
      std::move(searchComponents.fullTextExcludeMatcher);
  filePathHighlighter_ = std::move(searchComponents.filePathHighlighter);
  fullTextHighlighter_ = std::move(searchComponents.fullTextHighlighter);

  // 使用buildSearchComponents返回的解析后查询对象
  parsedFileNameQuery_ = std::move(searchComponents.parsedFileNameQuery);
  parsedFileNameExcludeQuery_ =
      std::move(searchComponents.parsedFileNameExcludeQuery);
  parsedFullTextQuery_ = std::move(searchComponents.parsedFullTextQuery);
  parsedFullTextExcludeQuery_ =
      std::move(searchComponents.parsedFullTextExcludeQuery);

  // 构建关键词提取器
  std::vector<std::u8string> terms;
  auto termsQuery =
      dynamic_cast<const TermsQuery *>(parsedFullTextQuery_.get());
  if (termsQuery) {
    for (auto &term : termsQuery->terms()) {
      terms.push_back(term.term());
    }
  }
  keywordExtractor_ = KeywordExtractor(terms, caseInsensive);
  // 设置排序选项
  Sort sort(sortProperty.toStdString().c_str(),
            sortOrderAsc ? Sort::Direction::ASC : Sort::Direction::DESC);

  // 设置搜索选项
  SearchEngineService::SearchOptions options;
  options.caseInsensive = caseInsensive;
  options.searchFullPath = searchFullPath;
  options.includeArchive = enableCompression;

  // 执行搜索
  searchEngineService_
      ->search2(parsedFileNameQuery_ ? parsedFileNameQuery_->clone() : nullptr,
                parsedFullTextQuery_ ? parsedFullTextQuery_->clone() : nullptr,
                sort, options)
      .then(*mainThreadExecutor_,
            [this, options,
             cancellationToken = searchTaskCancallationSource_.getToken()](
                boost::future<std::pair<SearchEngineService::TopHits, DocSet>>
                    &&f) mutable {
              // 处理搜索结果
              auto [topHits, docSet] = f.get();
              indexFileContent(docSet, options.includeArchive);
            })
      .get();
  // deletedFilesTable_.reset();
  // delete db_delete_files_;
  // 清空pathTable_中的数据
  if (pathTable_) {
    pathTable_->clearTable();
  }
  std::filesystem::path filepath_path((tmpDBPath_ + dbname).toStdString());
  try {
    std::filesystem::remove(filepath_path);
  } catch (const std::exception &e) {
  }
}
boost::future<DocSet>
BackgroundModel::filterNotIndexedDocSet(DocSet docSet,
                                  const CancellationToken &cancellationToken) {
  return boost::make_ready_future(std::move(docSet));
}

void  BackgroundModel::indexFileContent(
    const DocSet& docSet, bool includeArchive) {
  backgroundIndexFileContentTaskCancallationSource_ = CancellationSource();

  auto cancellationToken =
      backgroundIndexFileContentTaskCancallationSource_.getToken();

  return filterNotIndexedDocSet(docSet, cancellationToken)
          .then(*mainThreadExecutor_,
                [this, includeArchive,
                 cancellationToken](boost::future<DocSet> &&f) {
                  if (cancellationToken.isCancellationRequested())
                    throw std::runtime_error("cancelled");
                  auto docSet = f.get();
                  IndexFileContentTask indexFileContentTask(
                      configService_, searchEngineService_,
                      fileContentIndexService_, mainThreadExecutor_, ioTheadExecutor_);

                  return indexFileContentTask(docSet, includeArchive, [this](){return this->needToParse();},
                                              this,
                                              cancellationToken,
                                              [this]() -> bool {
                                                // 检查是否应该继续处理：如果已达到最大收集数则停止
                                                return collectCnt_ <= configService_->maxHits();
                                              },
                                              [](auto docId) {});
                })
          .unwrap().get();
}

boost::future<void> BackgroundModel::endIndexFileContent() {
  backgroundIndexFileContentTaskCancallationSource_.requestCancellation();
  return boost::make_ready_future();
}

bool BackgroundModel::filterFile(const std::filesystem::path &path) {
    //auto u8path = path.u8string();
    auto u8path = path.lexically_normal().generic_string();
    if (!filePathFilter_.match(
            QString::fromStdString(u8path.c_str()))) {
      return false;
    }
    auto format = fileFormatDetector_->detectFormat(path, CancellationToken());
    QString filePath = QString::fromStdString(u8path.c_str());
    QString ext = QString::fromStdString(getFileFormatName(format));
    if (!fileExtFilter_.match(filePath) && !fileExtFilter_.match(ext)) {
        return false;
    }
    // if (true || fileFormatAutoMatch_) {
    //   QString ext = QString::fromStdString(getFileFormatName(format));   
    //   if (!fileExtFilter_.match(ext)) {
    //     if (ext == ".doc" && !fileExtFilter_.match(QString(".wps"))) {
    //       if (!fileExtFilter_.match(filePath)) {
    //         return false;
    //       }
    //     } else if (ext == ".xls" && !fileExtFilter_.match(QString(".et"))) {
    //       if (!fileExtFilter_.match(filePath)) {
    //         return false;
    //       }
    //     } else if (ext == ".ppt" && !fileExtFilter_.match(QString(".dps"))) {
    //       if (!fileExtFilter_.match(filePath)) {
    //         return false;
    //       }
    //     } else if (ext != ".ppt" && ext!= ".doc" && ext!= ".xls" ) {
    //       if (!fileExtFilter_.match(filePath)) {
    //         return false;
    //       }
    //     }
    //   }
    // } else {
    //   if (!fileExtFilter_.match(filePath)) {
    //     return false;
    //   }
    // }
    if (fileNameTermsExcludeMatcher_ &&
        fileNameTermsExcludeMatcher_->match(path.u8string().c_str())) {
      return false;
    }
    if (fileNameTermsMatcher_ && deepSearch_ && !fileNameTermsMatcher_->match(path.u8string().c_str())) {
      return false;
    }
    return true;
}

static QString calculateFileMD5(const QString &filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) return QString();

    const qint64 bufferSize = 10 * 1024 * 1024; // 10MB缓冲区
    char *buffer = new char[bufferSize];
    QCryptographicHash hash(QCryptographicHash::Md5);

    while (!file.atEnd()) {
        qint64 bytesRead = file.read(buffer, bufferSize);
        hash.addData(buffer, bytesRead);
    }
    file.close();
    delete[] buffer;
    return hash.result().toHex();
}

bool BackgroundModel::needToParse() {
  if (fullTextQuery_.isEmpty()) {
    return false;
  }
  // return true;
  if (collectCnt_ > configService_->maxHits()) {
    return false;
  }
  return true;
}

void BackgroundModel::onDocIndexed(
  DocId id, const std::filesystem::path &path_, const std::u8string &text,
  std::optional<SearchEngineService::Document> doc,
  void *context) {
    if (collectCnt_ > configService_->maxHits()) {
      return;
    }
    if (!filterFile(path_)) {
      return;
    }
    if (fullTextTermsExcludeMatcher_ && fullTextTermsExcludeMatcher_->matchOnce(text.c_str())) {
      return;
    }
    bool deletedFile = false;
    if (doc.has_value()) {
      if (!doc->text().has_value())deletedFile = true;
    }
    //bool deletedFile = doc.has_value() ? true : false;
    if ((fullTextTermsMatcher_ && fullTextTermsMatcher_->matchOnce(text.c_str()))||!fullTextTermsMatcher_) {
      if (!doc.has_value())
        doc = searchEngineService_->getDocumentSync(id);
      if (doc) {
        auto fileDirHighlighted = doc->name().parent_path().u8string();
        auto fileNameHighlighted = doc->name().filename().u8string();
        if (filePathHighlighter_.has_value()) {
          /* TODO)) 不包含目录名称时, 要不要高亮文件名 */
          fileDirHighlighted =
              filePathHighlighter_.value()
                  .highlightAll(doc->name().parent_path().u8string())
                  .text();
          fileNameHighlighted =
              filePathHighlighter_.value()
                  .highlightAll(doc->name().filename().u8string())
                  .text();
        }
  
        // std::u8string fullTextHighlighted = text;
        auto path = doc->name();
        auto name = path.filename();
        auto dir = path.parent_path();
        QString fullTextHighlightedJsonStr = "[]";
        TextNormalizer textNormalizer;
        auto beautifyText = textNormalizer.normalize(text);
        if (fullTextHighlighter_.has_value()) {
          auto arry = fullTextHighlighter_.value().highlightAbstract(beautifyText);
          QJsonArray json;
          for (int i = 0 ;i < arry.size(); ++i) {
            json.append(QString::fromUtf8(reinterpret_cast<const char*>(arry[i].text().c_str())));
          }
          fullTextHighlightedJsonStr = QJsonDocument(json).toJson();
          // fullTextHighlighted =
          //     fullTextHighlighter_.value().highlightAbstract(beautifyText).text();
        } /*else {
            QJsonArray json;
            json.append(QString::fromUtf8(reinterpret_cast<const char*>(beautifyText.c_str())));
            fullTextHighlightedJsonStr = QJsonDocument(json).toJson();
        }*/
        std::vector<HitKeyword> keywords;
        keywords = keywordExtractor_.extractQuick(text);
        QJsonArray json;
        for (auto &keyword : keywords) {
          QJsonObject obj;
          obj.insert("keyword", QString::fromUtf8(reinterpret_cast<const char*>(keyword.keyword().c_str())));
          obj.insert("hit", keyword.count());
          json.append(obj);
        }
        auto kw = QString(QJsonDocument(json).toJson());
        //auto kw = QString("");
        {
          std::lock_guard<std::mutex> lock(collectCntMutex_);
          ++collectCnt_;
        }
        bool bviolation = ViolationProcessor::instance()->judge(path, text);
        QString pathStr = QString::fromUtf8(
            reinterpret_cast<const char *> (path.u8string().c_str()));
        QString md5 = doc->attributes().isDirectory() ? "" : calculateFileMD5(pathStr);
        auto taskResult = taskResultTable_.getByUniqueKey(path.string(), md5.toLocal8Bit().constData());
        if (taskResult.has_value()) return;
        while(true) {
            auto ret = taskResultTable_.insert(
                task_id_,
                path.string(), 
                name.string(),
                dir.string(),
                doc->size(), 
                std::chrono::system_clock::to_time_t(doc->creationTime()),
                std::chrono::system_clock::to_time_t(doc->lastModificationTime()),
                std::chrono::system_clock::to_time_t(doc->lastAccessTime()),
                QString::fromUtf8(reinterpret_cast<const char*>(fileNameHighlighted.c_str())).toLocal8Bit().constData(),
                fullTextHighlightedJsonStr.toLocal8Bit().constData(),
                QString::fromUtf8(reinterpret_cast<const char*>(fileDirHighlighted.c_str())).toLocal8Bit().constData(),
                doc->attributes().isRegularFile(),
                doc->attributes().isDirectory(),
                doc->attributes().isHidden(),
                doc->attributes().isSystem(),
                doc->attributes().isReadOnly(),
                std::time(nullptr), 
                0,
                kw.toLocal8Bit().constData(),
                md5.toLocal8Bit().constData(),
                timeEpoch_.toLocal8Bit().constData(),(bviolation?1:0),(deletedFile?1:0),
                bviolation?ViolationProcessor::instance()->getViolationTags() : ""
              );
              if (ret && bviolation) {
                QThread::msleep(1000);
                taskResult = taskResultTable_.getByUniqueKey(path.string(), md5.toLocal8Bit().constData());
                if (!taskResult.has_value()){
                  ret = false;
                } else {
                  if (deletedFile || uploadFileTable_.insert(md5.toStdString(),
                                                         path.string())) {
                    ret = ViolationProcessor::instance()->process(networkAccessManager_.get(), taskResult.value());
                  } else {
                    ret = false;
                  }
                }
              }
            if (ret) {
                break;
            } else {
              QThread::msleep(2000);
              SPDLOG_WARN("insert or process violation failed, retry");
            }
          }
      }
    }
}



BackgroundModel::SearchComponents BackgroundModel::buildSearchComponents(
    const QString& fileNameQuery,
    const QString& fileNameExcludeQuery,
    const QString& fullTextQuery,
    const QString& fullTextExcludeQuery,
    bool caseInsensive,
    int summaryLength,
    int summaryCount) {

  SearchComponents components;

  // 解析查询
  components.parsedFileNameQuery = queryParser_->parse(reinterpret_cast<const char8_t *>(
      fileNameQuery.toUtf8().constData()));
  components.parsedFileNameExcludeQuery = queryParser_->parse(reinterpret_cast<const char8_t *>(
      fileNameExcludeQuery.toUtf8().constData()));
  components.parsedFullTextQuery = queryParser_->parse(reinterpret_cast<const char8_t *>(
      fullTextQuery.toUtf8().constData()));
  components.parsedFullTextExcludeQuery = queryParser_->parse(reinterpret_cast<const char8_t *>(
      fullTextExcludeQuery.toUtf8().constData()));

  // 构建高亮器
  components.filePathHighlighter = components.parsedFileNameQuery
      ? std::make_optional<Highlighter>(
            highlighterFactory_->create(components.parsedFileNameQuery.get(), caseInsensive))
      : std::optional<Highlighter>();

  components.fullTextHighlighter = components.parsedFullTextQuery
      ? std::make_optional<Highlighter>(
            highlighterFactory_->create(components.parsedFullTextQuery.get(),
                true /* 全文匹配总是不区分大小写的 */, summaryLength, summaryCount))
      : std::optional<Highlighter>();

  // 构建匹配器（使用与原有方法相同的逻辑）
  if (components.parsedFileNameQuery) {
    auto termsQuery = dynamic_cast<const TermsQuery *>(components.parsedFileNameQuery.get());
    if (termsQuery) {
      TermsQueryMatchEngine<AutoMpmEngine> engine(termsQuery, true /* 文件名匹配也不区分大小写 */);
      components.fileNameMatcher = std::make_optional<TermsQueryMatcher<AutoMpmEngine>>(engine.matcher());
    }
  }

  if (components.parsedFileNameExcludeQuery) {
    auto termsQuery = dynamic_cast<const TermsQuery *>(components.parsedFileNameExcludeQuery.get());
    if (termsQuery) {
      TermsQueryMatchEngine<AutoMpmEngine> engine(termsQuery, true /* 文件名匹配也不区分大小写 */);
      components.fileNameExcludeMatcher = std::make_optional<TermsQueryMatcher<AutoMpmEngine>>(engine.matcher());
    }
  }

  if (components.parsedFullTextQuery) {
    auto termsQuery = dynamic_cast<const TermsQuery *>(components.parsedFullTextQuery.get());
    if (termsQuery) {
      TermsQueryMatchEngine<AutoMpmEngine> engine(termsQuery, true /* 全文匹配总是不区分大小写的 */);
      components.fullTextMatcher = std::make_optional<TermsQueryMatcher<AutoMpmEngine>>(engine.matcher());
    }
  }

  if (components.parsedFullTextExcludeQuery) {
    auto termsQuery = dynamic_cast<const TermsQuery *>(components.parsedFullTextExcludeQuery.get());
    if (termsQuery) {
      TermsQueryMatchEngine<AutoMpmEngine> engine(termsQuery, true /* 全文匹配总是不区分大小写的 */);
      components.fullTextExcludeMatcher = std::make_optional<TermsQueryMatcher<AutoMpmEngine>>(engine.matcher());
    }
  }

  // 设置配置和检查器
  components.configService = configService_;
  components.advancedChecker = advanceChecker_;

  return components;
}

} // namespace app
} // namespace anywhere
