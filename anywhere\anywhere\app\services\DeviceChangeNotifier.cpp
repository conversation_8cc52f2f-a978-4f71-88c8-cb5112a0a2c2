#include "DeviceChangeNotifier.h"
#include <spdlog/spdlog.h>
#include <chrono>
#include <thread>

namespace anywhere {
namespace app {

DeviceChangeNotifier::DeviceChangeNotifier()
    : running_(false), lastDrivesMask_(0) {
    // 初始化当前驱动器状态
    lastDrivesMask_ = GetLogicalDrives();
    SPDLOG_INFO("DeviceChangeNotifier initialized with polling-only mode, initial drives: 0x{:X}", lastDrivesMask_);
}

DeviceChangeNotifier::~DeviceChangeNotifier() {
    stop();
}

bool DeviceChangeNotifier::start() {
    if (running_) {
        return true;
    }

    running_ = true;

    // 只启动轮询线程
    pollingThread_ = std::thread(&DeviceChangeNotifier::pollingThreadFunc, this);

    SPDLOG_INFO("DeviceChangeNotifier started with polling-only detection (every 2 seconds)");
    return true;
}

void DeviceChangeNotifier::stop() {
    if (!running_) {
        return;
    }

    running_ = false;

    // 等待轮询线程结束
    if (pollingThread_.joinable()) {
        pollingThread_.join();
    }

    SPDLOG_INFO("DeviceChangeNotifier stopped");
}

void DeviceChangeNotifier::setCallback(DeviceChangeCallback callback) {
    callback_ = callback;
}

void DeviceChangeNotifier::pollingThreadFunc() {
    SPDLOG_INFO("DeviceChangeNotifier polling thread started");

    while (running_) {
        try {
            // 每2秒检查一次驱动器变更
            std::this_thread::sleep_for(std::chrono::seconds(2));

            if (!running_) {
                break;
            }

            // 获取当前驱动器掩码
            DWORD currentDrivesMask = GetLogicalDrives();

            // 检查是否有变更
            if (currentDrivesMask != lastDrivesMask_) {
                SPDLOG_INFO("Polling detected drive change: 0x{:X} -> 0x{:X}", lastDrivesMask_, currentDrivesMask);

                // 更新状态
                lastDrivesMask_ = currentDrivesMask;

                // 触发回调
                if (callback_) {
                    SPDLOG_INFO("Polling triggered device change callback");
                    callback_();
                }
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Error in polling thread: {}", e.what());
        } catch (...) {
            SPDLOG_ERROR("Unknown error in polling thread");
        }
    }

    SPDLOG_INFO("DeviceChangeNotifier polling thread stopped");
}

} // namespace app
} // namespace anywhere
