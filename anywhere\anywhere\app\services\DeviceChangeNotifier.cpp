#include "DeviceChangeNotifier.h"
#include <spdlog/spdlog.h>
#include <setupapi.h>
#include <devguid.h>
#include <chrono>
#include <thread>

namespace anywhere {
namespace app {

const wchar_t* DeviceChangeNotifier::WINDOW_CLASS_NAME = L"DeviceChangeNotifierWindow";
DeviceChangeNotifier* DeviceChangeNotifier::instance_ = nullptr;

DeviceChangeNotifier::DeviceChangeNotifier() 
    : running_(false), hwnd_(nullptr), hDevNotify_(nullptr) {
    instance_ = this;
}

DeviceChangeNotifier::~DeviceChangeNotifier() {
    stop();
    instance_ = nullptr;
}

bool DeviceChangeNotifier::start() {
    if (running_) {
        return true;
    }
    
    running_ = true;
    messageThread_ = std::thread(&DeviceChangeNotifier::messageLoopThread, this);
    
    // 等待窗口创建完成
    while (running_ && hwnd_ == nullptr) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    return hwnd_ != nullptr;
}

void DeviceChangeNotifier::stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    // 发送退出消息
    if (hwnd_) {
        PostMessage(hwnd_, WM_QUIT, 0, 0);
    }
    
    // 等待消息线程结束
    if (messageThread_.joinable()) {
        messageThread_.join();
    }
    
    SPDLOG_INFO("DeviceChangeNotifier stopped");
}

void DeviceChangeNotifier::setCallback(DeviceChangeCallback callback) {
    callback_ = callback;
}

void DeviceChangeNotifier::messageLoopThread() {
    // 注册窗口类
    WNDCLASSEXW wc = {};
    wc.cbSize = sizeof(WNDCLASSEXW);
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = GetModuleHandle(nullptr);
    wc.lpszClassName = WINDOW_CLASS_NAME;
    
    if (!RegisterClassExW(&wc)) {
        SPDLOG_ERROR("Failed to register window class for device change notification");
        running_ = false;
        return;
    }
    
    // 创建隐藏窗口
    hwnd_ = CreateWindowExW(
        0,
        WINDOW_CLASS_NAME,
        L"DeviceChangeNotifier",
        0,
        0, 0, 0, 0,
        HWND_MESSAGE,  // 消息窗口
        nullptr,
        GetModuleHandle(nullptr),
        nullptr
    );
    
    if (!hwnd_) {
        SPDLOG_ERROR("Failed to create window for device change notification");
        running_ = false;
        return;
    }
    
    // 注册设备通知 - 使用卷设备通知来捕获驱动器变更
    DEV_BROADCAST_VOLUME dbv = {};
    dbv.dbcv_size = sizeof(DEV_BROADCAST_VOLUME);
    dbv.dbcv_devicetype = DBT_DEVTYP_VOLUME;
    dbv.dbcv_unitmask = 0;  // 监控所有卷
    dbv.dbcv_flags = 0;     // 监控所有类型的卷

    hDevNotify_ = RegisterDeviceNotificationW(
        hwnd_,
        &dbv,
        DEVICE_NOTIFY_WINDOW_HANDLE
    );

    if (!hDevNotify_) {
        SPDLOG_ERROR("Failed to register volume device notification, Error: {}", GetLastError());

        // 作为备选方案，尝试不注册特定设备通知，只依赖WM_DEVICECHANGE消息
        SPDLOG_WARN("Will rely on general WM_DEVICECHANGE messages without specific registration");
    } else {
        SPDLOG_INFO("Successfully registered volume device notification");
    }
    
    SPDLOG_INFO("DeviceChangeNotifier started successfully");
    
    // 消息循环
    MSG msg;
    while (running_ && GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    // 清理
    if (hDevNotify_) {
        UnregisterDeviceNotification(hDevNotify_);
        hDevNotify_ = nullptr;
    }
    
    if (hwnd_) {
        DestroyWindow(hwnd_);
        hwnd_ = nullptr;
    }
    
    UnregisterClassW(WINDOW_CLASS_NAME, GetModuleHandle(nullptr));
}

LRESULT CALLBACK DeviceChangeNotifier::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    if (instance_) {
        if (uMsg == WM_DEVICECHANGE) {
            SPDLOG_INFO("Received WM_DEVICECHANGE message, wParam: 0x{:X}, lParam: 0x{:X}", wParam, lParam);
            instance_->handleDeviceChange(wParam, lParam);
        } else {
            // 记录其他消息以便调试
            if (uMsg != WM_TIMER && uMsg != WM_PAINT && uMsg != WM_NCHITTEST) {
                SPDLOG_DEBUG("Received message: 0x{:X}, wParam: 0x{:X}, lParam: 0x{:X}", uMsg, wParam, lParam);
            }
        }
    }

    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

void DeviceChangeNotifier::handleDeviceChange(WPARAM wParam, LPARAM lParam) {
    SPDLOG_DEBUG("handleDeviceChange called with wParam: 0x{:X}, lParam: 0x{:X}", wParam, lParam);

    switch (wParam) {
        case DBT_DEVICEARRIVAL:
            SPDLOG_INFO("Device arrival detected (DBT_DEVICEARRIVAL)");
            if (isStorageDevice(lParam)) {
                SPDLOG_INFO("Storage device connected - triggering callback");
                if (callback_) {
                    // 延迟一点时间，确保设备完全就绪
                    std::thread([this]() {
                        std::this_thread::sleep_for(std::chrono::milliseconds(500));
                        SPDLOG_INFO("Executing device change callback after delay");
                        callback_();
                    }).detach();
                }
            } else {
                SPDLOG_DEBUG("Device arrival detected but not a storage device");
            }
            break;

        case DBT_DEVICEREMOVECOMPLETE:
            SPDLOG_INFO("Device removal detected (DBT_DEVICEREMOVECOMPLETE)");
            if (isStorageDevice(lParam)) {
                SPDLOG_INFO("Storage device disconnected - triggering callback");
                if (callback_) {
                    callback_();
                }
            } else {
                SPDLOG_DEBUG("Device removal detected but not a storage device");
            }
            break;

        default:
            SPDLOG_DEBUG("Unhandled device change event: 0x{:X}", wParam);
            break;
    }
}

bool DeviceChangeNotifier::isStorageDevice(LPARAM lParam) {
    if (!lParam) {
        SPDLOG_DEBUG("isStorageDevice: lParam is null - treating as storage device");
        // 如果lParam为空，我们仍然认为这可能是存储设备变更
        return true;
    }

    PDEV_BROADCAST_HDR pHdr = reinterpret_cast<PDEV_BROADCAST_HDR>(lParam);
    SPDLOG_INFO("isStorageDevice: device type = {}", pHdr->dbch_devicetype);

    // 检查是否是卷设备
    if (pHdr->dbch_devicetype == DBT_DEVTYP_VOLUME) {
        PDEV_BROADCAST_VOLUME pVol = reinterpret_cast<PDEV_BROADCAST_VOLUME>(pHdr);
        SPDLOG_INFO("isStorageDevice: volume flags = 0x{:X}, unit mask = 0x{:X}",
                     pVol->dbcv_flags, pVol->dbcv_unitmask);

        // 对于卷设备，我们认为都是存储设备（包括U盘、硬盘等）
        // 不再严格检查DBTF_MEDIA标志，因为某些U盘可能没有设置这个标志
        SPDLOG_INFO("isStorageDevice: volume device detected - treating as storage device");
        return true;
    } else if (pHdr->dbch_devicetype == DBT_DEVTYP_DEVICEINTERFACE) {
        SPDLOG_INFO("isStorageDevice: device interface detected - treating as storage device");
        // 对于设备接口，我们也认为可能是存储设备
        return true;
    }

    SPDLOG_INFO("isStorageDevice: unknown device type {} - treating as storage device", pHdr->dbch_devicetype);
    // 为了确保不遗漏任何存储设备，我们对未知类型也返回true
    return true;
}

} // namespace app
} // namespace anywhere
