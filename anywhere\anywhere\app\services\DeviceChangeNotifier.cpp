#include "DeviceChangeNotifier.h"
#include <spdlog/spdlog.h>
#include <setupapi.h>
#include <devguid.h>
#include <chrono>
#include <thread>

namespace anywhere {
namespace app {

const wchar_t* DeviceChangeNotifier::WINDOW_CLASS_NAME = L"DeviceChangeNotifierWindow";
DeviceChangeNotifier* DeviceChangeNotifier::instance_ = nullptr;

DeviceChangeNotifier::DeviceChangeNotifier()
    : running_(false), hwnd_(nullptr), hDevNotify_(nullptr), lastDrivesMask_(0) {
    instance_ = this;
    // 初始化当前驱动器状态
    lastDrivesMask_ = GetLogicalDrives();
}

DeviceChangeNotifier::~DeviceChangeNotifier() {
    stop();
    instance_ = nullptr;
}

bool DeviceChangeNotifier::start() {
    if (running_) {
        return true;
    }

    running_ = true;

    // 启动消息循环线程
    messageThread_ = std::thread(&DeviceChangeNotifier::messageLoopThread, this);

    // 启动轮询线程作为备选方案
    pollingThread_ = std::thread(&DeviceChangeNotifier::pollingThreadFunc, this);

    // 等待窗口创建完成
    while (running_ && hwnd_ == nullptr) {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    SPDLOG_INFO("DeviceChangeNotifier started with both message-based and polling detection");
    return hwnd_ != nullptr;
}

void DeviceChangeNotifier::stop() {
    if (!running_) {
        return;
    }

    running_ = false;

    // 发送退出消息
    if (hwnd_) {
        PostMessage(hwnd_, WM_QUIT, 0, 0);
    }

    // 等待消息线程结束
    if (messageThread_.joinable()) {
        messageThread_.join();
    }

    // 等待轮询线程结束
    if (pollingThread_.joinable()) {
        pollingThread_.join();
    }

    SPDLOG_INFO("DeviceChangeNotifier stopped");
}

void DeviceChangeNotifier::setCallback(DeviceChangeCallback callback) {
    callback_ = callback;
}

void DeviceChangeNotifier::messageLoopThread() {
    // 注册窗口类
    WNDCLASSEXW wc = {};
    wc.cbSize = sizeof(WNDCLASSEXW);
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = GetModuleHandle(nullptr);
    wc.lpszClassName = WINDOW_CLASS_NAME;
    
    if (!RegisterClassExW(&wc)) {
        SPDLOG_ERROR("Failed to register window class for device change notification");
        running_ = false;
        return;
    }
    
    // 创建隐藏窗口 - 使用普通窗口而不是消息窗口，因为消息窗口可能无法接收设备变更通知
    hwnd_ = CreateWindowExW(
        0,
        WINDOW_CLASS_NAME,
        L"DeviceChangeNotifier",
        WS_OVERLAPPED,  // 使用普通窗口样式
        CW_USEDEFAULT, CW_USEDEFAULT, 1, 1,  // 最小尺寸
        nullptr,  // 不使用HWND_MESSAGE
        nullptr,
        GetModuleHandle(nullptr),
        nullptr
    );

    // 隐藏窗口
    if (hwnd_) {
        ShowWindow(hwnd_, SW_HIDE);
    }
    
    if (!hwnd_) {
        SPDLOG_ERROR("Failed to create window for device change notification");
        running_ = false;
        return;
    }
    
    // 不注册特定的设备通知，直接依赖WM_DEVICECHANGE广播消息
    // 这种方法更简单且更可靠，因为Windows会向所有顶级窗口广播WM_DEVICECHANGE消息
    SPDLOG_INFO("DeviceChangeNotifier window created, will receive WM_DEVICECHANGE broadcast messages");

    // 可选：如果需要，可以尝试注册设备通知作为额外保障
    DEV_BROADCAST_DEVICEINTERFACE dbdi = {};
    dbdi.dbcc_size = sizeof(DEV_BROADCAST_DEVICEINTERFACE);
    dbdi.dbcc_devicetype = DBT_DEVTYP_DEVICEINTERFACE;

    hDevNotify_ = RegisterDeviceNotificationW(
        hwnd_,
        &dbdi,
        DEVICE_NOTIFY_WINDOW_HANDLE | DEVICE_NOTIFY_ALL_INTERFACE_CLASSES
    );

    if (hDevNotify_) {
        SPDLOG_INFO("Successfully registered additional device interface notification");
    } else {
        SPDLOG_DEBUG("Device interface notification registration failed (Error: {}), but will still receive broadcast messages", GetLastError());
    }
    
    SPDLOG_INFO("DeviceChangeNotifier started successfully");
    
    // 消息循环
    MSG msg;
    while (running_ && GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    // 清理
    if (hDevNotify_) {
        UnregisterDeviceNotification(hDevNotify_);
        hDevNotify_ = nullptr;
    }
    
    if (hwnd_) {
        DestroyWindow(hwnd_);
        hwnd_ = nullptr;
    }
    
    UnregisterClassW(WINDOW_CLASS_NAME, GetModuleHandle(nullptr));
}

LRESULT CALLBACK DeviceChangeNotifier::WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    if (instance_) {
        if (uMsg == WM_DEVICECHANGE) {
            SPDLOG_INFO("Received WM_DEVICECHANGE message, wParam: 0x{:X}, lParam: 0x{:X}", wParam, lParam);
            instance_->handleDeviceChange(wParam, lParam);
        } else {
            // 记录其他消息以便调试
            if (uMsg != WM_TIMER && uMsg != WM_PAINT && uMsg != WM_NCHITTEST) {
                SPDLOG_DEBUG("Received message: 0x{:X}, wParam: 0x{:X}, lParam: 0x{:X}", uMsg, wParam, lParam);
            }
        }
    }

    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

void DeviceChangeNotifier::handleDeviceChange(WPARAM wParam, LPARAM lParam) {
    SPDLOG_DEBUG("handleDeviceChange called with wParam: 0x{:X}, lParam: 0x{:X}", wParam, lParam);

    switch (wParam) {
        case DBT_DEVICEARRIVAL:
            SPDLOG_INFO("Device arrival detected (DBT_DEVICEARRIVAL)");
            if (isStorageDevice(lParam)) {
                SPDLOG_INFO("Storage device connected - triggering callback");
                if (callback_) {
                    // 延迟一点时间，确保设备完全就绪
                    std::thread([this]() {
                        std::this_thread::sleep_for(std::chrono::milliseconds(500));
                        SPDLOG_INFO("Executing device change callback after delay");
                        callback_();
                    }).detach();
                }
            } else {
                SPDLOG_DEBUG("Device arrival detected but not a storage device");
            }
            break;

        case DBT_DEVICEREMOVECOMPLETE:
            SPDLOG_INFO("Device removal detected (DBT_DEVICEREMOVECOMPLETE)");
            if (isStorageDevice(lParam)) {
                SPDLOG_INFO("Storage device disconnected - triggering callback");
                if (callback_) {
                    callback_();
                }
            } else {
                SPDLOG_DEBUG("Device removal detected but not a storage device");
            }
            break;

        default:
            SPDLOG_DEBUG("Unhandled device change event: 0x{:X}", wParam);
            break;
    }
}

bool DeviceChangeNotifier::isStorageDevice(LPARAM lParam) {
    if (!lParam) {
        SPDLOG_DEBUG("isStorageDevice: lParam is null - treating as storage device");
        // 如果lParam为空，我们仍然认为这可能是存储设备变更
        return true;
    }

    PDEV_BROADCAST_HDR pHdr = reinterpret_cast<PDEV_BROADCAST_HDR>(lParam);
    SPDLOG_INFO("isStorageDevice: device type = {}", pHdr->dbch_devicetype);

    // 检查是否是卷设备
    if (pHdr->dbch_devicetype == DBT_DEVTYP_VOLUME) {
        PDEV_BROADCAST_VOLUME pVol = reinterpret_cast<PDEV_BROADCAST_VOLUME>(pHdr);
        SPDLOG_INFO("isStorageDevice: volume flags = 0x{:X}, unit mask = 0x{:X}",
                     pVol->dbcv_flags, pVol->dbcv_unitmask);

        // 对于卷设备，我们认为都是存储设备（包括U盘、硬盘等）
        // 不再严格检查DBTF_MEDIA标志，因为某些U盘可能没有设置这个标志
        SPDLOG_INFO("isStorageDevice: volume device detected - treating as storage device");
        return true;
    } else if (pHdr->dbch_devicetype == DBT_DEVTYP_DEVICEINTERFACE) {
        SPDLOG_INFO("isStorageDevice: device interface detected - treating as storage device");
        // 对于设备接口，我们也认为可能是存储设备
        return true;
    }

    SPDLOG_INFO("isStorageDevice: unknown device type {} - treating as storage device", pHdr->dbch_devicetype);
    // 为了确保不遗漏任何存储设备，我们对未知类型也返回true
    return true;
}

void DeviceChangeNotifier::pollingThreadFunc() {
    SPDLOG_INFO("DeviceChangeNotifier polling thread started");

    while (running_) {
        try {
            // 每2秒检查一次驱动器变更
            std::this_thread::sleep_for(std::chrono::seconds(2));

            if (!running_) {
                break;
            }

            // 获取当前驱动器掩码
            DWORD currentDrivesMask = GetLogicalDrives();

            // 检查是否有变更
            if (currentDrivesMask != lastDrivesMask_) {
                SPDLOG_INFO("Polling detected drive change: 0x{:X} -> 0x{:X}", lastDrivesMask_, currentDrivesMask);

                // 更新状态
                lastDrivesMask_ = currentDrivesMask;

                // 触发回调
                if (callback_) {
                    SPDLOG_INFO("Polling triggered device change callback");
                    callback_();
                }
            }
        } catch (const std::exception& e) {
            SPDLOG_ERROR("Error in polling thread: {}", e.what());
        } catch (...) {
            SPDLOG_ERROR("Unknown error in polling thread");
        }
    }

    SPDLOG_INFO("DeviceChangeNotifier polling thread stopped");
}

} // namespace app
} // namespace anywhere
