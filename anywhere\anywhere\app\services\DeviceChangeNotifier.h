#ifndef DEVICE_CHANGE_NOTIFIER_H
#define DEVICE_CHANGE_NOTIFIER_H

#include <windows.h>
#include <functional>
#include <thread>
#include <atomic>

namespace anywhere {
namespace app {

/**
 * 设备变更通知器
 * 使用Windows消息机制实时检测设备插拔
 */
class DeviceChangeNotifier {
public:
    // 设备变更回调函数类型
    using DeviceChangeCallback = std::function<void()>;
    
    DeviceChangeNotifier();
    ~DeviceChangeNotifier();
    
    // 启动设备变更监控
    bool start();
    
    // 停止设备变更监控
    void stop();
    
    // 设置设备变更回调
    void setCallback(DeviceChangeCallback callback);
    
    // 检查是否正在运行
    bool isRunning() const { return running_; }

private:
    
    std::atomic<bool> running_;
    std::thread pollingThread_;  // 只使用轮询线程
    DeviceChangeCallback callback_;

    // 用于轮询检测的状态
    DWORD lastDrivesMask_;

    // 轮询线程函数
    void pollingThreadFunc();
};

} // namespace app
} // namespace anywhere

#endif // DEVICE_CHANGE_NOTIFIER_H
