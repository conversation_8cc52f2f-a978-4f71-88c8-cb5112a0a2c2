#include <spdlog/spdlog.h>
#include <windows.h>
#include <lmcons.h>
#include <psapi.h>
#undef max
#undef min
#include <chrono>
#include <filesystem>
#include <algorithm>
#include <cctype>
#include "FileFormat.h"
#include "CancellationToken.h"
#include "FileChangeService.h"
#include "TaskManager.h"

namespace anywhere {
namespace app {

FileChangeService* FileChangeService::instance_ = nullptr;

FileChangeService& FileChangeService::instance() {
    if (instance_ == nullptr) {
        instance_ = new FileChangeService();
    }
    return *instance_;
}

FileChangeService::FileChangeService()
    : running_(false)
    , databasePath_(getSystemDatabasePath())
    , batchSize_(100)
    , batchTimeoutMs_(2000)
    , maxQueueSize_(50000)
    , totalEventsReceived_(0)
    , totalRecordsWritten_(0)
    , droppedEvents_(0)
    , currentProcessId_(GetCurrentProcessId()) {
    
    // 获取当前用户名
    currentUserName_ = getCurrentUserName();

    // 获取当前进程名
    auto processInfo = getCurrentProcessInfo();
    currentProcessName_ = processInfo.first;

    // 初始化支持的文件扩展名
    initializeFileExtensions();
}

FileChangeService::~FileChangeService() {
    stop();
}

bool FileChangeService::start() {
    if (running_) {
        return true;
    }
    
    try {
        // 初始化数据库
        database_ = std::make_unique<Database>(databasePath_);
        if (!database_->isOpen()) {
            SPDLOG_ERROR("Failed to open database: {}", databasePath_);
            return false;
        }
        
        changeLogTable_ = std::make_unique<FileChangeLogTable>(*database_);
        if (!changeLogTable_->createTable()) {
            SPDLOG_ERROR("Failed to create file change log table");
            return false;
        }
        
        // 启动数据库写入线程
        running_ = true;
        writerThread_ = std::thread(&FileChangeService::databaseWriterThread, this);
        
        // 设置文件系统监控回调
        FileSystemWatcher::instance().setEventCallback(
            [this](const FileChangeEvent& event) {
                onFileChanged(event);
            });
        
        // 启动文件系统监控
        if (!FileSystemWatcher::instance().start()) {
            SPDLOG_ERROR("Failed to start file system watcher");
            stop();
            return false;
        }
        
        SPDLOG_INFO("FileChangeService started successfully");
        return true;
        
    } catch (const std::exception& e) {
        SPDLOG_ERROR("Exception starting FileChangeService: {}", e.what());
        stop();
        return false;
    }
}

void FileChangeService::stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    // 停止文件系统监控
    FileSystemWatcher::instance().stop();
    
    // 刷新待写入的记录
    flushPendingRecords();
    
    // 通知写入线程退出
    queueCondition_.notify_all();
    
    // 等待写入线程结束
    if (writerThread_.joinable()) {
        writerThread_.join();
    }
    
    // 关闭数据库
    changeLogTable_.reset();
    database_.reset();
    
    SPDLOG_INFO("FileChangeService stopped");
}

void FileChangeService::onFileChanged(const FileChangeEvent& event) {
    totalEventsReceived_++;

    // 检查文件类型过滤
    if (!isMonitoredFileType(event.filePath)) {
        return; // 跳过不监控的文件类型
    }

    // 添加调试日志
    std::string eventTypeStr;
    switch (event.type) {
        case FileChangeType::Created: eventTypeStr = "Created"; break;
        case FileChangeType::Modified: eventTypeStr = "Modified"; break;
        case FileChangeType::Deleted: eventTypeStr = "Deleted"; break;
        case FileChangeType::Renamed: eventTypeStr = "Renamed"; break;
        default: eventTypeStr = "Unknown"; break;
    }

    std::string filePath = std::string(event.filePath.begin(), event.filePath.end());
     SPDLOG_INFO("File change event: {} - {}", eventTypeStr, filePath);

    // 转换为数据库记录
    auto record = eventToRecord(event);

    // 添加到待写入队列
    {
        std::lock_guard<std::mutex> lock(queueMutex_);

        // 检查队列大小，防止内存溢出
        if (pendingRecords_.size() >= maxQueueSize_) {
            droppedEvents_++;
            SPDLOG_WARN("Dropping file change event due to full queue");
            return;
        }

        pendingRecords_.push(record);
    }

    // 通知写入线程
    queueCondition_.notify_one();
}

void FileChangeService::databaseWriterThread() {
    auto lastFlushTime = std::chrono::steady_clock::now();
    
    while (running_) {
        std::unique_lock<std::mutex> lock(queueMutex_);
        
        // 等待事件或超时
        queueCondition_.wait_for(lock, std::chrono::milliseconds(batchTimeoutMs_), [this] {
            return !pendingRecords_.empty() || !running_;
        });
        
        if (!running_) {
            break;
        }
        
        auto now = std::chrono::steady_clock::now();
        bool shouldFlush = !pendingRecords_.empty() && 
            (pendingRecords_.size() >= batchSize_ || 
             (now - lastFlushTime) >= std::chrono::milliseconds(batchTimeoutMs_));
        
        if (shouldFlush) {
            // 收集批次记录
            std::vector<FileChangeLogTable::FileChangeRecord> batch;
            batch.reserve(batchSize_);
            
            while (!pendingRecords_.empty() && batch.size() < batchSize_) {
                batch.push_back(std::move(pendingRecords_.front()));
                pendingRecords_.pop();
            }
            
            lock.unlock();
            
            // 批量写入数据库
            if (changeLogTable_ && !batch.empty()) {
                // SPDLOG_INFO("Writing {} file change records to database", batch.size());
                if (changeLogTable_->insertRecords(batch)) {
                    totalRecordsWritten_ += batch.size();
                    // SPDLOG_INFO("Successfully wrote {} records to database", batch.size());
                } else {
                    SPDLOG_ERROR("Failed to insert file change records to database");
                    droppedEvents_ += batch.size();
                }
            }
            
            lastFlushTime = now;
        }
    }
    
    // 处理剩余的记录
    flushPendingRecords();
}

void FileChangeService::flushPendingRecords() {
    std::lock_guard<std::mutex> lock(queueMutex_);
    
    if (pendingRecords_.empty() || !changeLogTable_) {
        return;
    }
    
    std::vector<FileChangeLogTable::FileChangeRecord> batch;
    while (!pendingRecords_.empty()) {
        batch.push_back(std::move(pendingRecords_.front()));
        pendingRecords_.pop();
    }
    
    if (changeLogTable_->insertRecords(batch)) {
        totalRecordsWritten_ += batch.size();
        SPDLOG_INFO("Flushed {} pending file change records", batch.size());
    } else {
        SPDLOG_ERROR("Failed to flush pending file change records");
        droppedEvents_ += batch.size();
    }
}

FileChangeLogTable::FileChangeRecord FileChangeService::eventToRecord(const FileChangeEvent& event) {
    FileChangeLogTable::FileChangeRecord record;
    
    // 转换文件路径为UTF-8
    int utf8Size = WideCharToMultiByte(CP_UTF8, 0, event.filePath.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (utf8Size > 0) {
        std::string utf8Path(utf8Size - 1, '\0');
        WideCharToMultiByte(CP_UTF8, 0, event.filePath.c_str(), -1, &utf8Path[0], utf8Size, nullptr, nullptr);
        record.filePath = utf8Path;
    }
    
    // 转换旧文件路径（用于重命名）
    if (!event.oldFilePath.empty()) {
        int oldUtf8Size = WideCharToMultiByte(CP_UTF8, 0, event.oldFilePath.c_str(), -1, nullptr, 0, nullptr, nullptr);
        if (oldUtf8Size > 0) {
            std::string oldUtf8Path(oldUtf8Size - 1, '\0');
            WideCharToMultiByte(CP_UTF8, 0, event.oldFilePath.c_str(), -1, &oldUtf8Path[0], oldUtf8Size, nullptr, nullptr);
            record.oldFilePath = oldUtf8Path;
        }
    }
    
    // 设置其他字段
    record.changeType = static_cast<int>(event.type);
    record.fileSize = event.fileSize;
    record.fileAttributes = event.fileAttributes;
    record.timestamp = fileTimeToUnixTime(event.timestamp);
    record.driveLetter = extractDriveLetter(event.filePath);
    record.processName = currentProcessName_;
    record.processId = currentProcessId_;
    record.userName = currentUserName_;
    record.createdTime = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    record.isProcessed = false; // 新记录默认未处理

    return record;
}

std::string FileChangeService::getCurrentUserName() {
    char username[UNLEN + 1];
    DWORD usernameLen = UNLEN + 1;
    if (GetUserNameA(username, &usernameLen)) {
        return std::string(username);
    }
    return "Unknown";
}

std::pair<std::string, uint32_t> FileChangeService::getCurrentProcessInfo() {
    char processName[MAX_PATH];
    DWORD processNameLen = MAX_PATH;

    HANDLE hProcess = GetCurrentProcess();
    if (GetModuleBaseNameA(hProcess, nullptr, processName, processNameLen)) {
        return {std::string(processName), GetCurrentProcessId()};
    }

    return {"Unknown", GetCurrentProcessId()};
}

std::string FileChangeService::extractDriveLetter(const std::wstring& filePath) {
    if (filePath.length() >= 2 && filePath[1] == L':') {
        return std::string(1, static_cast<char>(filePath[0]));
    }
    return "";
}

int64_t FileChangeService::fileTimeToUnixTime(const FILETIME& ft) {
    // Windows FILETIME是从1601年1月1日开始的100纳秒间隔数
    // Unix时间戳是从1970年1月1日开始的秒数
    const int64_t EPOCH_DIFFERENCE = 11644473600LL; // 1970-1601的秒数差

    ULARGE_INTEGER uli;
    uli.LowPart = ft.dwLowDateTime;
    uli.HighPart = ft.dwHighDateTime;

    // 转换为秒
    return (uli.QuadPart / 10000000LL) - EPOCH_DIFFERENCE;
}

FileSystemWatcher::Statistics FileChangeService::getWatcherStatistics() const {
    return FileSystemWatcher::instance().getStatistics();
}

FileChangeLogTable::Statistics FileChangeService::getDatabaseStatistics() const {
    if (changeLogTable_) {
        return changeLogTable_->getStatistics();
    }
    return {};
}

std::vector<FileChangeLogTable::FileChangeRecord> FileChangeService::getRecentRecords(int limit) {
    if (changeLogTable_) {
        return changeLogTable_->getRecords(limit);
    }
    return {};
}

std::vector<FileChangeLogTable::FileChangeRecord> FileChangeService::getRecordsByPath(
    const std::string& path, int limit) {
    if (changeLogTable_) {
        return changeLogTable_->getRecordsByPath(path, limit);
    }
    return {};
}

std::vector<FileChangeLogTable::FileChangeRecord> FileChangeService::getRecordsByTimeRange(
    int64_t startTime, int64_t endTime, int limit) {
    if (changeLogTable_) {
        return changeLogTable_->getRecordsByTimeRange(startTime, endTime, limit);
    }
    return {};
}

bool FileChangeService::cleanupOldRecords(int daysToKeep) {
    if (changeLogTable_) {
        return changeLogTable_->cleanupOldRecords(daysToKeep);
    }
    return false;
}

void FileChangeService::setDatabasePath(const std::string& dbPath) {
    if (!running_) {
        databasePath_ = dbPath;
    }
}

void FileChangeService::setBatchSize(size_t batchSize) {
    batchSize_ = batchSize;
}

void FileChangeService::setBatchTimeout(int timeoutMs) {
    batchTimeoutMs_ = timeoutMs;
}

void FileChangeService::setMaxQueueSize(size_t maxSize) {
    maxQueueSize_ = maxSize;
}

std::string FileChangeService::getSystemDatabasePath() {
    // 获取AppData\Roaming路径
    char* appDataPath = nullptr;
    size_t len = 0;
    if (_dupenv_s(&appDataPath, &len, "APPDATA") != 0 || appDataPath == nullptr) {
        SPDLOG_ERROR("Failed to get APPDATA environment variable");
        return "system.db"; // 回退到当前目录
    }

    std::string appDataStr(appDataPath);
    std::string dirPath = appDataStr + "\\anywhere";
    std::string dbPath = dirPath + "\\system.db";

    free(appDataPath);

    // 确保目录存在
    CreateDirectoryA(dirPath.c_str(), nullptr);

    return dbPath;
}

void FileChangeService::initializeFileExtensions() {
    // 文档类型
    monitoredExtensions_.insert(L".doc");
    monitoredExtensions_.insert(L".docx");
    monitoredExtensions_.insert(L".xls");
    monitoredExtensions_.insert(L".xlsx");
    monitoredExtensions_.insert(L".xlsm");
    monitoredExtensions_.insert(L".xlt");
    monitoredExtensions_.insert(L".xltm");
    monitoredExtensions_.insert(L".xltx");
    monitoredExtensions_.insert(L".ppt");
    monitoredExtensions_.insert(L".pptx");
    monitoredExtensions_.insert(L".pdf");
    monitoredExtensions_.insert(L".txt");
    monitoredExtensions_.insert(L".rtf");
    monitoredExtensions_.insert(L".odt");
    monitoredExtensions_.insert(L".ods");
    monitoredExtensions_.insert(L".odp");
    monitoredExtensions_.insert(L".wps");
    monitoredExtensions_.insert(L".et");
    monitoredExtensions_.insert(L".dps");
    monitoredExtensions_.insert(L".csv");
    monitoredExtensions_.insert(L".xml");
    monitoredExtensions_.insert(L".json");
    monitoredExtensions_.insert(L".md");
    monitoredExtensions_.insert(L".ini");
    monitoredExtensions_.insert(L".conf");
    monitoredExtensions_.insert(L".cfg");
    monitoredExtensions_.insert(L".uot");
    monitoredExtensions_.insert(L".t");
    monitoredExtensions_.insert(L".eis");

    // 图片类型
    monitoredExtensions_.insert(L".jpg");
    monitoredExtensions_.insert(L".jpeg");
    monitoredExtensions_.insert(L".png");
    monitoredExtensions_.insert(L".gif");
    monitoredExtensions_.insert(L".bmp");
    monitoredExtensions_.insert(L".tiff");
    monitoredExtensions_.insert(L".raw");
    monitoredExtensions_.insert(L".psd");
    monitoredExtensions_.insert(L".ai");
    monitoredExtensions_.insert(L".svg");
    monitoredExtensions_.insert(L".webp");
    monitoredExtensions_.insert(L".ico");
    monitoredExtensions_.insert(L".heic");
    monitoredExtensions_.insert(L".nef");
    monitoredExtensions_.insert(L".cr2");
    monitoredExtensions_.insert(L".arw");

    // 邮件和压缩文件类型
    monitoredExtensions_.insert(L".eml");
    monitoredExtensions_.insert(L".msg");
    monitoredExtensions_.insert(L".pst");
    monitoredExtensions_.insert(L".ost");
    monitoredExtensions_.insert(L".dbx");
    monitoredExtensions_.insert(L".zip");
    monitoredExtensions_.insert(L".rar");
    monitoredExtensions_.insert(L".7z");
    monitoredExtensions_.insert(L".tar");
    monitoredExtensions_.insert(L".gz");
    monitoredExtensions_.insert(L".arj");
    monitoredExtensions_.insert(L".iso");
    monitoredExtensions_.insert(L".lzma");
    monitoredExtensions_.insert(L".bz2");
    monitoredExtensions_.insert(L".html");

    SPDLOG_INFO("Initialized file extension filter with {} supported types", monitoredExtensions_.size());
}

bool FileChangeService::isMonitoredFileType(const std::wstring& filePath) {
    // 首先检查 TaskManager 的 taskEntriesSize_ 是否大于 0
    auto& taskManager = TaskManager::instance();
    if (taskManager && taskManager->getTaskEntriesSize() == 0) {
        return false; // 如果没有任务条目，不监控任何文件
    }

    // 查找最后一个点的位置
    size_t dotPos = filePath.find_last_of(L'.');
    if (dotPos == std::wstring::npos) {
        // 没有扩展名，如果有文件格式检测器，尝试检测
        if (fileFormatDetector_) {
            try {
                std::filesystem::path path(filePath);
                CancellationToken token;
                auto format = fileFormatDetector_->detectFormat(path, token);
                return format != anywhere::FileFormat::UNKNOWN && format != anywhere::FileFormat::TXT;
            } catch (...) {
                return false;
            }
        }
        return false;
    }

    // 提取扩展名并转换为小写
    std::wstring extension = filePath.substr(dotPos);
    std::transform(extension.begin(), extension.end(), extension.begin(), ::towlower);

    // 首先检查是否在监控列表中
    if (monitoredExtensions_.find(extension) != monitoredExtensions_.end()) {
        return true;
    }

    // 如果不在监控列表中，但有文件格式检测器，尝试检测
    if (fileFormatDetector_) {
        try {
            std::filesystem::path path(filePath);
            CancellationToken token;
            auto format = fileFormatDetector_->detectFormat(path, token);
            return format != anywhere::FileFormat::UNKNOWN;
        } catch (...) {
            return false;
        }
    }

    return false;
}

std::vector<FileChangeLogTable::FileChangeRecord> FileChangeService::getUnprocessedRecords(int limit) {
    if (!changeLogTable_) {
        return {};
    }
    return changeLogTable_->getUnprocessedRecords(limit);
}

bool FileChangeService::markRecordAsProcessed(int64_t recordId) {
    if (!changeLogTable_) {
        return false;
    }
    return changeLogTable_->markAsProcessed(recordId);
}

bool FileChangeService::markRecordsAsProcessed(const std::vector<int64_t>& recordIds) {
    if (!changeLogTable_) {
        return false;
    }
    return changeLogTable_->markAsProcessed(recordIds);
}

void FileChangeService::setFileFormatDetector(std::shared_ptr<anywhere::parser::FileFormatDetector> detector) {
    fileFormatDetector_ = detector;
}

} // namespace app
} // namespace anywhere
