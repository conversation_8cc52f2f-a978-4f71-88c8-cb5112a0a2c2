#include "FileSystemWatcher.h"
#include "TaskManager.h"
#include <spdlog/spdlog.h>
#include <algorithm>
#include <chrono>

namespace anywhere {
namespace app {

FileSystemWatcher* FileSystemWatcher::instance_ = nullptr;

// DriveWatcher 实现
DriveWatcher::DriveWatcher(const std::wstring& drivePath, HANDLE iocp)
    : drivePath_(drivePath), dirHandle_(INVALID_HANDLE_VALUE), iocp_(iocp), running_(false) {
    ZeroMemory(&overlapped_, sizeof(overlapped_));
    // 在IOCP模式下，不需要设置hEvent，使用completionKey来标识DriveWatcher
}

DriveWatcher::~DriveWatcher() {
    stop();
}

bool DriveWatcher::start() {
    if (running_) {
        return true;
    }
    
    if (!setupDirectoryWatch()) {
        SPDLOG_ERROR("Failed to setup directory watch for drive: {}", 
                     std::string(drivePath_.begin(), drivePath_.end()));
        return false;
    }
    
    running_ = true;
    SPDLOG_INFO("Started monitoring drive: {}", 
                std::string(drivePath_.begin(), drivePath_.end()));
    return true;
}

void DriveWatcher::stop() {
    if (!running_) {
        return;
    }
    
    running_ = false;
    
    if (dirHandle_ != INVALID_HANDLE_VALUE) {
        CancelIo(dirHandle_);
        CloseHandle(dirHandle_);
        dirHandle_ = INVALID_HANDLE_VALUE;
    }
    
    SPDLOG_INFO("Stopped monitoring drive: {}", 
                std::string(drivePath_.begin(), drivePath_.end()));
}

bool DriveWatcher::setupDirectoryWatch() {
    // 打开驱动器根目录
    dirHandle_ = CreateFileW(
        drivePath_.c_str(),
        FILE_LIST_DIRECTORY,
        FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE,
        nullptr,
        OPEN_EXISTING,
        FILE_FLAG_BACKUP_SEMANTICS | FILE_FLAG_OVERLAPPED,
        nullptr
    );
    
    if (dirHandle_ == INVALID_HANDLE_VALUE) {
        SPDLOG_ERROR("Failed to open directory handle for drive: {}, Error: {}", 
                     std::string(drivePath_.begin(), drivePath_.end()), GetLastError());
        return false;
    }
    
    // 将目录句柄关联到I/O完成端口
    if (CreateIoCompletionPort(dirHandle_, iocp_, reinterpret_cast<ULONG_PTR>(this), 0) == nullptr) {
        SPDLOG_ERROR("Failed to associate directory with IOCP for drive: {}, Error: {}", 
                     std::string(drivePath_.begin(), drivePath_.end()), GetLastError());
        CloseHandle(dirHandle_);
        dirHandle_ = INVALID_HANDLE_VALUE;
        return false;
    }
    
    // 开始异步监控
    DWORD bytesReturned;
    BOOL result = ReadDirectoryChangesW(
        dirHandle_,
        buffer_,
        BUFFER_SIZE,
        TRUE,  // 监控子目录
        FILE_NOTIFY_CHANGE_FILE_NAME | FILE_NOTIFY_CHANGE_DIR_NAME | 
        FILE_NOTIFY_CHANGE_SIZE | FILE_NOTIFY_CHANGE_LAST_WRITE | FILE_NOTIFY_CHANGE_CREATION,
        &bytesReturned,
        &overlapped_,
        nullptr
    );
    
    if (!result && GetLastError() != ERROR_IO_PENDING) {
        SPDLOG_ERROR("Failed to start ReadDirectoryChangesW for drive: {}, Error: {}", 
                     std::string(drivePath_.begin(), drivePath_.end()), GetLastError());
        return false;
    }
    
    return true;
}

void DriveWatcher::processNotifications(DWORD bytesTransferred) {
    if (bytesTransferred == 0 || !running_) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(eventsMutex_);
    
    PFILE_NOTIFY_INFORMATION pNotify = reinterpret_cast<PFILE_NOTIFY_INFORMATION>(buffer_);
    
    while (pNotify) {
        // 构建完整文件路径
        std::wstring fileName(pNotify->FileName, pNotify->FileNameLength / sizeof(WCHAR));
        std::wstring fullPath = drivePath_ + fileName;
        
        // 创建文件变更事件
        FileChangeEvent event;
        event.filePath = fullPath;
        GetSystemTimeAsFileTime(&event.timestamp);
        
        // 获取文件属性和大小
        WIN32_FIND_DATAW findData;
        HANDLE hFind = FindFirstFileW(fullPath.c_str(), &findData);
        if (hFind != INVALID_HANDLE_VALUE) {
            event.fileSize = findData.nFileSizeLow;
            event.fileAttributes = findData.dwFileAttributes;
            FindClose(hFind);
        }
        
        // 根据通知类型设置事件类型
        switch (pNotify->Action) {
            case FILE_ACTION_ADDED:
                event.type = FileChangeType::Created;
                break;
            case FILE_ACTION_REMOVED:
                event.type = FileChangeType::Deleted;
                break;
            case FILE_ACTION_MODIFIED:
                event.type = FileChangeType::Modified;
                break;
            case FILE_ACTION_RENAMED_OLD_NAME:
                // 暂存旧名称，等待新名称
                pendingEvents_[fullPath] = event;
                pendingEvents_[fullPath].type = FileChangeType::Renamed;
                break;
            case FILE_ACTION_RENAMED_NEW_NAME:
                // 查找对应的旧名称
                for (auto it = pendingEvents_.begin(); it != pendingEvents_.end(); ++it) {
                    if (it->second.type == FileChangeType::Renamed) {
                        event.type = FileChangeType::Renamed;
                        event.oldFilePath = it->first;
                        pendingEvents_.erase(it);
                        break;
                    }
                }
                if (event.oldFilePath.empty()) {
                    event.type = FileChangeType::Created;  // 如果找不到旧名称，当作创建处理
                }
                break;
        }
        
        // 事件聚合：相同文件的连续事件可能需要合并
        auto existingEvent = pendingEvents_.find(fullPath);
        if (existingEvent != pendingEvents_.end() &&
            existingEvent->second.type != FileChangeType::Renamed) {
            // 更新现有事件的时间戳
            existingEvent->second.timestamp = event.timestamp;
            existingEvent->second.fileSize = event.fileSize;
            existingEvent->second.fileAttributes = event.fileAttributes;
        } else {
            pendingEvents_[fullPath] = event;
        }

        // 添加调试日志
        std::string eventTypeStr;
        switch (event.type) {
            case FileChangeType::Created: eventTypeStr = "Created"; break;
            case FileChangeType::Modified: eventTypeStr = "Modified"; break;
            case FileChangeType::Deleted: eventTypeStr = "Deleted"; break;
            case FileChangeType::Renamed: eventTypeStr = "Renamed"; break;
            default: eventTypeStr = "Unknown"; break;
        }
        std::string filePath = std::string(event.filePath.begin(), event.filePath.end());
        // SPDLOG_INFO("DriveWatcher detected: {} - {}", eventTypeStr, filePath);

        // 将事件添加到FileSystemWatcher的队列中
        FileSystemWatcher::instance().addEventToQueue(event);
        
        // 移动到下一个通知
        if (pNotify->NextEntryOffset == 0) {
            break;
        }
        pNotify = reinterpret_cast<PFILE_NOTIFY_INFORMATION>(
            reinterpret_cast<BYTE*>(pNotify) + pNotify->NextEntryOffset);
    }
    
    // 重新开始监控
    if (running_) {
        ZeroMemory(&overlapped_, sizeof(overlapped_));

        DWORD bytesReturned;
        ReadDirectoryChangesW(
            dirHandle_,
            buffer_,
            BUFFER_SIZE,
            TRUE,
            FILE_NOTIFY_CHANGE_FILE_NAME | FILE_NOTIFY_CHANGE_DIR_NAME |
            FILE_NOTIFY_CHANGE_SIZE | FILE_NOTIFY_CHANGE_LAST_WRITE | FILE_NOTIFY_CHANGE_CREATION,
            &bytesReturned,
            &overlapped_,
            nullptr
        );
    }
}

// FileSystemWatcher 实现
FileSystemWatcher& FileSystemWatcher::instance() {
    if (instance_ == nullptr) {
        instance_ = new FileSystemWatcher();
    }
    return *instance_;
}

FileSystemWatcher::FileSystemWatcher()
    : running_(false), iocp_(INVALID_HANDLE_VALUE) {
    ZeroMemory(&stats_, sizeof(stats_));
}

FileSystemWatcher::~FileSystemWatcher() {
    stop();
}

bool FileSystemWatcher::start() {
    if (running_) {
        return true;
    }

    // 创建I/O完成端口
    iocp_ = CreateIoCompletionPort(INVALID_HANDLE_VALUE, nullptr, 0, 0);
    if (iocp_ == nullptr) {
        SPDLOG_ERROR("Failed to create I/O completion port, Error: {}", GetLastError());
        return false;
    }

    // 设置运行标志，必须在启动线程之前
    running_ = true;

    // 初始化驱动器监控器
    initializeDriveWatchers();

    // 启动工作线程（使用CPU核心数）
    SYSTEM_INFO sysInfo;
    GetSystemInfo(&sysInfo);
    size_t numThreads = (sysInfo.dwNumberOfProcessors > 2) ? sysInfo.dwNumberOfProcessors : 2;

    for (size_t i = 0; i < numThreads; ++i) {
        workerThreads_.emplace_back(&FileSystemWatcher::workerThread, this);
    }

    // 启动事件处理线程
    eventProcessorThread_ = std::thread(&FileSystemWatcher::eventProcessorThread, this);

    // 启动设备变更通知器
    deviceNotifier_ = std::make_unique<DeviceChangeNotifier>();
    deviceNotifier_->setCallback([this]() {
        onDeviceChange();
    });

    if (!deviceNotifier_->start()) {
        SPDLOG_WARN("Failed to start device change notifier, using periodic check");
    }
    SPDLOG_INFO("FileSystemWatcher started with {} worker threads", numThreads);
    return true;
}

void FileSystemWatcher::stop() {
    if (!running_) {
        return;
    }

    running_ = false;

    // 停止设备变更通知器
    if (deviceNotifier_) {
        deviceNotifier_->stop();
        deviceNotifier_.reset();
    }

    // 停止所有驱动器监控器
    {
        std::lock_guard<std::mutex> lock(driveWatchersMutex_);
        for (auto& watcher : driveWatchers_) {
            watcher->stop();
        }
        driveWatchers_.clear();
    }

    // 通知所有工作线程退出
    if (iocp_ != INVALID_HANDLE_VALUE) {
        for (size_t i = 0; i < workerThreads_.size(); ++i) {
            PostQueuedCompletionStatus(iocp_, 0, 0, nullptr);
        }
    }

    // 等待工作线程结束
    for (auto& thread : workerThreads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    workerThreads_.clear();

    // 通知事件处理线程退出
    queueCondition_.notify_all();
    if (eventProcessorThread_.joinable()) {
        eventProcessorThread_.join();
    }

    // 关闭I/O完成端口
    if (iocp_ != INVALID_HANDLE_VALUE) {
        CloseHandle(iocp_);
        iocp_ = INVALID_HANDLE_VALUE;
    }

    SPDLOG_INFO("FileSystemWatcher stopped");
}

void FileSystemWatcher::setEventCallback(EventCallback callback) {
    std::lock_guard<std::mutex> lock(callbackMutex_);
    eventCallback_ = callback;
}

void FileSystemWatcher::addEventToQueue(const FileChangeEvent& event) {
    std::lock_guard<std::mutex> lock(queueMutex_);
    eventQueue_.push(event);
    queueCondition_.notify_one();
}

void FileSystemWatcher::initializeDriveWatchers() {
    // 获取所有逻辑驱动器
    DWORD drives = GetLogicalDrives();

    std::lock_guard<std::mutex> currentLock(currentDrivesMutex_);
    std::lock_guard<std::mutex> watchersLock(driveWatchersMutex_);

    for (int i = 0; i < 26; ++i) {
        if (drives & (1 << i)) {
            wchar_t driveLetter = L'A' + i;
            std::wstring drivePath = std::wstring(1, driveLetter) + L":\\";

            // 检查驱动器类型，支持所有可访问的驱动器
            UINT driveType = GetDriveTypeW(drivePath.c_str());
            if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE ||
                driveType == DRIVE_CDROM || driveType == DRIVE_RAMDISK) {

                // 检查驱动器是否可访问
                if (GetFileAttributesW(drivePath.c_str()) != INVALID_FILE_ATTRIBUTES) {
                    auto watcher = std::make_unique<DriveWatcher>(drivePath, iocp_);
                    if (watcher->start()) {
                        driveWatchers_.push_back(std::move(watcher));
                        currentDrives_.insert(drivePath);
                        std::string driveTypeStr = getDriveTypeString(driveType);
                        SPDLOG_INFO("Added watcher for {} drive: {}", driveTypeStr,
                                   std::string(drivePath.begin(), drivePath.end()));
                    }
                    if (driveType != DRIVE_FIXED) {
                      std::string driveStr = drivePathToString(drivePath);
                      TaskManager::instance()->addAppendDriver(driveStr);
                    }
                } else {
                    SPDLOG_DEBUG("Drive not accessible: {}",
                               std::string(drivePath.begin(), drivePath.end()));
                }
            }
        }
    }
}

void FileSystemWatcher::workerThread() {
    DWORD bytesTransferred;
    ULONG_PTR completionKey;
    LPOVERLAPPED overlapped;

    while (running_) {
        BOOL result = GetQueuedCompletionStatus(
            iocp_,
            &bytesTransferred,
            &completionKey,
            &overlapped,
            1000  // 1秒超时
        );

        if (!result) {
            DWORD error = GetLastError();
            if (error == WAIT_TIMEOUT) {
                continue;  // 超时，继续循环
            }
            if (error == ERROR_ABANDONED_WAIT_0) {
                break;  // I/O完成端口被关闭
            }
            SPDLOG_WARN("GetQueuedCompletionStatus failed, Error: {}", error);
            continue;
        }

        // 检查是否是退出信号
        if (bytesTransferred == 0 && completionKey == 0 && overlapped == nullptr) {
            break;
        }

        // 处理文件变更通知
        if (overlapped != nullptr && completionKey != 0) {
            DriveWatcher* watcher = reinterpret_cast<DriveWatcher*>(completionKey);
            if (watcher != nullptr) {
                // SPDLOG_INFO("Processing file change notification, bytes: {}", bytesTransferred);
                watcher->processNotifications(bytesTransferred);

                // 更新统计信息
                std::lock_guard<std::mutex> lock(statsMutex_);
                stats_.totalEvents++;
            }
        }
    }
}

void FileSystemWatcher::eventProcessorThread() {
    auto lastBatchTime = std::chrono::steady_clock::now();

    while (running_) {
        std::unique_lock<std::mutex> lock(queueMutex_);

        // 等待事件或超时
        queueCondition_.wait_for(lock, BATCH_TIMEOUT, [this] {
            return !eventQueue_.empty() || !running_;
        });

        if (!running_) {
            break;
        }

        auto now = std::chrono::steady_clock::now();
        bool shouldProcess = !eventQueue_.empty() &&
            (eventQueue_.size() >= MAX_BATCH_SIZE ||
             (now - lastBatchTime) >= BATCH_TIMEOUT);

        if (shouldProcess) {
            processBatchEvents();
            lastBatchTime = now;
        }
    }
}

void FileSystemWatcher::processBatchEvents() {
    std::vector<FileChangeEvent> batch;
    batch.reserve(MAX_BATCH_SIZE);

    // 从队列中取出事件
    while (!eventQueue_.empty() && batch.size() < MAX_BATCH_SIZE) {
        batch.push_back(std::move(eventQueue_.front()));
        eventQueue_.pop();
    }

    if (batch.empty()) {
        return;
    }

    // 处理事件批次
    {
        std::lock_guard<std::mutex> callbackLock(callbackMutex_);
        if (eventCallback_) {
            for (const auto& event : batch) {
                try {
                    eventCallback_(event);

                    // 更新统计信息
                    std::lock_guard<std::mutex> statsLock(statsMutex_);
                    stats_.processedEvents++;
                    switch (event.type) {
                        case FileChangeType::Created:
                            stats_.createdFiles++;
                            break;
                        case FileChangeType::Modified:
                            stats_.modifiedFiles++;
                            break;
                        case FileChangeType::Deleted:
                            stats_.deletedFiles++;
                            break;
                        case FileChangeType::Renamed:
                            stats_.renamedFiles++;
                            break;
                    }
                } catch (const std::exception& e) {
                    SPDLOG_ERROR("Error processing file change event: {}", e.what());
                    std::lock_guard<std::mutex> statsLock(statsMutex_);
                    stats_.droppedEvents++;
                }
            }
        }
    }
}

FileSystemWatcher::Statistics FileSystemWatcher::getStatistics() const {
    std::lock_guard<std::mutex> lock(statsMutex_);
    return stats_;
}

void FileSystemWatcher::resetStatistics() {
    std::lock_guard<std::mutex> lock(statsMutex_);
    ZeroMemory(&stats_, sizeof(stats_));
}

std::string FileSystemWatcher::getDriveTypeString(UINT driveType) {
    switch (driveType) {
        case DRIVE_FIXED:
            return "Fixed";
        case DRIVE_REMOVABLE:
            return "Removable";
        case DRIVE_CDROM:
            return "CD-ROM";
        case DRIVE_RAMDISK:
            return "RAM Disk";
        case DRIVE_REMOTE:
            return "Network";
        default:
            return "Unknown";
    }
}

std::string FileSystemWatcher::drivePathToString(const std::wstring& drivePath) {
    // 转换 wstring 到 string，保留完整的驱动器路径（如 "C:\\"）
    std::string driveStr(drivePath.begin(), drivePath.end());
    return driveStr;
}

void FileSystemWatcher::onDeviceChange() {
    SPDLOG_INFO("onDeviceChange() called - checking for drive changes");

    // 获取当前所有逻辑驱动器
    std::set<std::wstring> newDrives;
    DWORD drives = GetLogicalDrives();
    SPDLOG_DEBUG("Current logical drives mask: 0x{:X}", drives);

    for (int i = 0; i < 26; ++i) {
        if (drives & (1 << i)) {
            wchar_t driveLetter = L'A' + i;
            std::wstring drivePath = std::wstring(1, driveLetter) + L":\\";

            // 检查驱动器类型和可访问性
            UINT driveType = GetDriveTypeW(drivePath.c_str());
            if (driveType == DRIVE_FIXED || driveType == DRIVE_REMOVABLE ||
                driveType == DRIVE_CDROM || driveType == DRIVE_RAMDISK) {

                if (GetFileAttributesW(drivePath.c_str()) != INVALID_FILE_ATTRIBUTES) {
                    newDrives.insert(drivePath);
                }
            }
        }
    }

    std::lock_guard<std::mutex> currentLock(currentDrivesMutex_);
    std::lock_guard<std::mutex> watchersLock(driveWatchersMutex_);

    // 检查新增的驱动器
    for (const auto& drivePath : newDrives) {
        if (currentDrives_.find(drivePath) == currentDrives_.end()) {
            // 新增驱动器，添加监控
            UINT driveType = GetDriveTypeW(drivePath.c_str());
            auto watcher = std::make_unique<DriveWatcher>(drivePath, iocp_);
            if (watcher->start()) {
                driveWatchers_.push_back(std::move(watcher));
                std::string driveTypeStr = getDriveTypeString(driveType);
                SPDLOG_INFO("Added watcher for new {} drive: {}", driveTypeStr,
                           std::string(drivePath.begin(), drivePath.end()));

                // 将新增的驱动器添加到 TaskManager 的 appendDrivers
                // 但是排除 CD-ROM 驱动器
                if (driveType != DRIVE_FIXED) {
                    std::string driveStr = drivePathToString(drivePath);
                    TaskManager::instance()->addAppendDriver(driveStr);
                }
            }
        }
    }

    // 检查移除的驱动器
    auto it = driveWatchers_.begin();
    while (it != driveWatchers_.end()) {
        const std::wstring& watcherPath = (*it)->getDrivePath();
        if (newDrives.find(watcherPath) == newDrives.end()) {
            // 驱动器已移除，停止监控
            (*it)->stop();
            SPDLOG_INFO("Removed watcher for drive: {}",
                       std::string(watcherPath.begin(), watcherPath.end()));

            // 从 TaskManager 的 appendDrivers 中移除该驱动器
            // 但是排除 CD-ROM 驱动器（因为它们本来就不在 appendDrivers 中）
            UINT driveType = GetDriveTypeW(watcherPath.c_str());
            if (driveType != DRIVE_CDROM) {
                std::string driveStr = drivePathToString(watcherPath);
                TaskManager::instance()->removeAppendDriver(driveStr);
            }

            it = driveWatchers_.erase(it);
        } else {
            ++it;
        }
    }

    // 更新当前驱动器列表
    currentDrives_ = newDrives;
}

} // namespace app
} // namespace anywhere
