#include "IncrementalSearchProcessor.h"
#include <QStandardPaths>
#include <QDebug>
#include <QJsonArray>
#include <QJsonObject>
#include <QJsonDocument>
#include <QFile>
#include <QIODevice>
#include <QCryptographicHash>
#include <chrono>
#include <thread>
#include <sstream>
#include "core/ServiceRegistry.h"
#include "core/services/ConfigService.h"
#include "core/services/SearchEngineService.h"
#include "core/services/FileContentIndexService.h"
#include "core/services/EventBusService.h"
#include "Miscs.h"  // 包含各种ServiceToken定义
#include "Executors.h"  // 包含MainExecutorToken和IoExecutorToken
#include "parser/DefaultFileParseEngine.h"
#include "parser/DefaultFileFormatDetector.h"
#include "FileFormat.h"
#include "KeywordExtractor.h"
#include "TextNormalizer.h"
#include "FileExtCheckerImpl.h"
#include "TermsQueryMatchEngine.h"

namespace anywhere {
namespace app {

using core::ConfigServiceToken;
using core::EventBusServiceToken;
using core::ServiceRegistry;

// 计算文件MD5值的辅助函数
static QString calculateFileMD5(const QString &filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) return QString();

    const qint64 bufferSize = 10 * 1024 * 1024; // 10MB缓冲区
    char *buffer = new char[bufferSize];
    QCryptographicHash hash(QCryptographicHash::Md5);

    while (!file.atEnd()) {
        qint64 bytesRead = file.read(buffer, bufferSize);
        hash.addData(buffer, bytesRead);
    }
    file.close();
    delete[] buffer;
    return hash.result().toHex();
}

IncrementalSearchProcessor::IncrementalSearchProcessor(const std::string& taskId)
    : taskId_(taskId)
    , running_(false)
    , db_((QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/system.db").toStdString())
    , uploadFileTable_(db_) {
    
    // 初始化数据库表
    fileChangeLogTable_ = std::make_shared<FileChangeLogTable>(db_);
    taskResultTable_ = std::make_shared<TaskResultTable>(db_);
    taskTable_ = std::make_shared<TaskTable>(db_);
    
    // 获取服务依赖（临时使用，后续会在updateSearchParameters中更新）
    auto tempConfigService = ServiceRegistry::instance()->getService<ConfigServiceToken>();
    configService_ = tempConfigService;
    
    auto eventBusService = ServiceRegistry::instance()->getService<EventBusServiceToken>();
    auto mainThreadExecutor = ServiceRegistry::instance()->getService<MainExecutorToken>();
    auto ioExecutor = ServiceRegistry::instance()->getService<IoExecutorToken>();
    
    // 创建搜索引擎服务
    searchEngineService_ = std::make_shared<core::SearchEngineService>(
        configService_, eventBusService, mainThreadExecutor, ioExecutor);
    
    // 创建文件解析引擎
    std::filesystem::path currentLibPath;
    auto tmpDir = std::filesystem::temp_directory_path();
    auto resourceReader = ServiceRegistry::instance()->getService<ResourceReaderToken>();
    
    auto defaultFileParseEngine = std::make_shared<parser::DefaultFileParseEngine>(
        tmpDir, currentLibPath, resourceReader,
        configService_->enabledFileFormats(),
        configService_->maxCharactorsPerFile());

    defaultFileParseEngine->setFileExtChecker(std::make_shared<FileExtCheckerImpl>());

    // 创建文件格式检测器
    fileFormatDetector_ = std::make_shared<parser::DefaultFileFormatDetector>(tmpDir, resourceReader);

    // 创建文件内容索引服务
    fileContentIndexService_ = std::make_shared<core::FileContentIndexService>(
        configService_, searchEngineService_, defaultFileParseEngine,
        eventBusService, mainThreadExecutor, ioExecutor);

    // 将自己设置为FileContentIndexService的docProcessor
    // 注意：这里不能直接使用shared_from_this()，因为对象还在构造中
    // 需要在start()方法中设置
}

IncrementalSearchProcessor::~IncrementalSearchProcessor() {
    stop();
}

void IncrementalSearchProcessor::start() {
    if (running_.load()) {
        return; // 已经在运行
    }

    qDebug() << "Starting incremental search processor for task:" << QString::fromStdString(taskId_);

    // 将自己设置为FileContentIndexService的docProcessor
    if (fileContentIndexService_) {
        fileContentIndexService_->setDocProcessor(shared_from_this());
    }

    running_.store(true);
    processingThread_ = std::thread(&IncrementalSearchProcessor::processLoop, this);
}

void IncrementalSearchProcessor::stop() {
    if (!running_.load()) {
        return; // 没有在运行
    }

    qDebug() << "Stopping incremental search processor for task:" << QString::fromStdString(taskId_);

    running_.store(false);
    if (processingThread_.joinable()) {
        processingThread_.join();
    }

    // 清除docProcessor设置
    if (fileContentIndexService_) {
        fileContentIndexService_->setDocProcessor(nullptr);
    }
}

void IncrementalSearchProcessor::updateSearchParameters(
    const FilePathFilter& pathFilter,
    const FileExtFilter& extFilter,
    std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fileNameMatcher,
    std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fileNameExcludeMatcher,
    std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fullTextMatcher,
    std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fullTextExcludeMatcher,
    std::optional<Highlighter>&& filePathHighlighter,
    std::optional<Highlighter>&& fullTextHighlighter,
    std::shared_ptr<core::ConfigService> configService,
    const AdvancedChecker& advancedChecker,
    const Query* parsedFullTextQuery) {

    // 更新过滤器
    filePathFilter_ = pathFilter;
    fileExtFilter_ = extFilter;

    // 更新匹配器（使用移动语义）
    fileNameMatcher_ = std::move(fileNameMatcher);
    fileNameExcludeMatcher_ = std::move(fileNameExcludeMatcher);
    fullTextMatcher_ = std::move(fullTextMatcher);
    fullTextExcludeMatcher_ = std::move(fullTextExcludeMatcher);

    // 更新高亮器
    filePathHighlighter_ = std::move(filePathHighlighter);
    fullTextHighlighter_ = std::move(fullTextHighlighter);

    // 更新配置和检查器
    configService_ = configService;
    advancedChecker_ = advancedChecker;

    // 保存查询对象的副本（如果提供）
    if (parsedFullTextQuery) {
        // 创建查询对象的副本
        savedParsedFullTextQuery_ = parsedFullTextQuery->clone();
    } else {
        savedParsedFullTextQuery_.reset();
    }

    // 构造 KeywordExtractor
    std::vector<std::u8string> terms;
    if (savedParsedFullTextQuery_) {
        auto termsQuery = dynamic_cast<const TermsQuery*>(savedParsedFullTextQuery_.get());
        if (termsQuery) {
            for (auto& term : termsQuery->terms()) {
                terms.push_back(term.term());
            }
        }
    }

    // 获取大小写敏感设置
    keywordExtractor_ = KeywordExtractor(terms, true);

    qDebug() << "Updated search parameters for task:" << QString::fromStdString(taskId_);
}

void IncrementalSearchProcessor::processLoop() {
    const int batchSize = 50; // 每次处理50条记录
    const int sleepSeconds = 3; // 没有记录时休眠3秒
    
    while (running_.load()) {
        try {
            // 获取未处理的文件变更记录
            auto unprocessedRecords = fileChangeLogTable_->getUnprocessedRecords(batchSize);
            
            if (unprocessedRecords.empty()) {
                // 没有未处理的记录，休眠一段时间
                std::this_thread::sleep_for(std::chrono::seconds(sleepSeconds));
                continue;
            }
            
            qDebug() << "Processing" << unprocessedRecords.size() << "file change records";
            
            // 处理每个文件变更记录
            for (const auto& record : unprocessedRecords) {
                if (!running_.load()) {
                    break; // 检查是否需要停止
                }

                processFileChange(record);
                fileChangeLogTable_->markAsProcessed(record.id);
            }
            static auto lastCleanupTime = std::chrono::steady_clock::now();
            auto currentTime = std::chrono::steady_clock::now();
            auto timeSinceLastCleanup = std::chrono::duration_cast<std::chrono::hours>(
                currentTime - lastCleanupTime).count();

            // 每24小时执行一次清理，避免频繁清理影响性能
            if (timeSinceLastCleanup >= 24) {
                qDebug() << "Starting cleanup of old file change records (older than 30 days)";

                // 删除30天前的记录（1个月 ≈ 30天）
                bool cleanupSuccess = fileChangeLogTable_->cleanupOldRecords(30);

                if (cleanupSuccess) {
                    qDebug() << "Successfully cleaned up old file change records";
                } else {
                    qWarning() << "Failed to cleanup old file change records";
                }

                lastCleanupTime = currentTime;
            }
        } catch (const std::exception& e) {
            qWarning() << "Error in incremental search processing:" << e.what();
            std::this_thread::sleep_for(std::chrono::seconds(10));
        }
    }
}

void IncrementalSearchProcessor::processFileChange(const FileChangeLogTable::FileChangeRecord& record) {
    // 正确处理UTF-8编码的文件路径
    std::filesystem::path filePath;
    try {
        // 在Windows上，需要将UTF-8字符串转换为宽字符
        QString qPath = QString::fromUtf8(record.filePath.c_str());
        filePath = std::filesystem::path(qPath.toStdWString());
    } catch (const std::exception& e) {
        qDebug() << "Failed to create file path from UTF-8 string:" << QString::fromUtf8(record.filePath.c_str()) << "Error:" << e.what();
        return;
    }
    
    // 检查文件是否应该被处理
    if (!shouldProcessFile(filePath)) {
        return;
    }
    
    try {
        // 根据变更类型处理
        switch (static_cast<FileChangeLogTable::ChangeType>(record.changeType)) {
            case FileChangeLogTable::ChangeType::Created:
            case FileChangeLogTable::ChangeType::Modified: {
                // 对于创建或修改的文件，进行索引和检索
                if (std::filesystem::exists(filePath)) {
                    CancellationToken token;
                    // 生成一个DocId（可以使用文件路径的哈希值）
                    DocId docId = std::hash<std::string>{}(filePath.string());
                    // 将record的副本作为context传递给indexDocument，以便在onDocIndexed中使用文件信息
                    // auto* recordCopy = new FileChangeLogTable::FileChangeRecord(record);
                    fileContentIndexService_->indexDocumentSync(docId, filePath, true, (void*)(&record), token, [this]() { 
                        if (!savedParsedFullTextQuery_) {
                            return false;
                        }
                        return true;
                    });
                }
                break;
            }
            
            case FileChangeLogTable::ChangeType::Deleted: {
                // 对于删除的文件，从结果中移除相关记录
                // TODO: 需要实现TaskResultTable的deleteByFilePath方法
                // taskResultTable_->deleteByFilePath(record.filePath);
                qDebug() << "File deleted:" << QString::fromStdString(record.filePath);
                // 删除操作不需要异步索引，直接标记为已处理
                
                break;
            }

            case FileChangeLogTable::ChangeType::Renamed: {
                // 对于重命名的文件，更新路径并重新检索
                if (!record.oldFilePath.empty()) {
                    // TODO: 需要实现TaskResultTable的deleteByFilePath方法
                    // taskResultTable_->deleteByFilePath(record.oldFilePath);
                    qDebug() << "File renamed from:" << QString::fromStdString(record.oldFilePath);
                }

                if (std::filesystem::exists(filePath)) {
                    CancellationToken token;
                    // 生成一个DocId（可以使用文件路径的哈希值）
                    DocId docId = std::hash<std::string>{}(filePath.string());
                    // 将record的副本作为context传递给indexDocument
                    // auto* recordCopy = new FileChangeLogTable::FileChangeRecord(record);
                    fileContentIndexService_->indexDocumentSync(docId, filePath, true, (void*)(&record), token, [this]() { 
                        if (!savedParsedFullTextQuery_) {
                            return false;
                        }
                        return true;
                    });
                } 
                break;
            }
        }
        
    } catch (const std::exception& e) {
        qWarning() << "Error processing file change for" << QString::fromStdString(record.filePath) 
                   << ":" << e.what();
    }
}

bool IncrementalSearchProcessor::shouldProcessFile(const std::filesystem::path& filePath) {
    // 检查文件路径过滤器
    // 确保正确处理UTF-8编码的路径
    QString pathStr;
    try {
        // 在Windows上，从宽字符路径转换为QString
        pathStr = QString::fromStdWString(filePath.lexically_normal().generic_wstring());
    } catch (const std::exception& e) {
        qDebug() << "Failed to convert file path to QString:" << e.what();
        return false;
    }
    if (!filePathFilter_.match(pathStr)) {
        return false;
    }
    
    // 使用fileFormatDetector_检测文件格式
    QString ext;
    try {
        auto format = fileFormatDetector_->detectFormat(filePath, CancellationToken());
        ext = QString::fromStdString(getFileFormatName(format));
    } catch (const std::exception &e) {
        qDebug() << "Failed to detectFormat file:" << e.what();
        return false;
    }
    // 检查文件路径和检测到的格式扩展名
    if (!fileExtFilter_.match(pathStr) && !fileExtFilter_.match(ext)) {
        return false;
    }
    // 检查文件名匹配
    if (fileNameMatcher_.has_value()) {
        std::u8string fileName = filePath.filename().u8string();
        if (!fileNameMatcher_->matchOnce(fileName.c_str())) {
            return false;
        }
    }

    // 检查文件名排除
    if (fileNameExcludeMatcher_.has_value()) {
        std::u8string fileName = filePath.filename().u8string();
        if (fileNameExcludeMatcher_->matchOnce(fileName.c_str())) {
            return false;
        }
    }

    return true;
}

void IncrementalSearchProcessor::onDocIndexed(DocId id, const std::filesystem::path& path,
                                            const std::u8string& text,
                                            std::optional<core::SearchEngineService::Document> doc,
                                            void* context) {
    // 如果doc为空，使用path构造一个Document
    core::SearchEngineService::Document actualDoc;
    if (doc.has_value()) {
        actualDoc = doc.value();
    } else {
        // 使用record中的文件信息构造Document
        try {
            // 从context中获取record信息
            auto* recordPtr = static_cast<FileChangeLogTable::FileChangeRecord*>(context);
            if (!recordPtr) {
                qDebug() << "Context is null, cannot get file info from record";
                // 使用默认值
                auto currentTime = std::chrono::system_clock::now();
                actualDoc = core::SearchEngineService::Document(
                    id, path,
                    FileAttributes(FileAttributes::Attributes::NORMAL),
                    0, currentTime, currentTime, currentTime,
                    std::make_optional(text)
                );
            } else {
                // 使用record中的真实文件信息
                const auto& record = *recordPtr;
                uint64_t fileSize = static_cast<uint64_t>(record.fileSize);
                FileAttributes::Attributes fileAttribs = static_cast<FileAttributes::Attributes>(record.fileAttributes);

                // 获取真实的文件创建时间
                std::chrono::system_clock::time_point createdTime;
                std::chrono::system_clock::time_point modificationTime;
                std::chrono::system_clock::time_point accessTime;

                try {
                    if (std::filesystem::exists(path)) {
                        // Windows 文件时间获取
                        WIN32_FILE_ATTRIBUTE_DATA fileData;
                        if (GetFileAttributesExA(path.string().c_str(), GetFileExInfoStandard, &fileData)) {
                            // 转换 FILETIME 到 system_clock
                            auto convertFileTime = [](const FILETIME& ft) -> std::chrono::system_clock::time_point {
                                ULARGE_INTEGER ull;
                                ull.LowPart = ft.dwLowDateTime;
                                ull.HighPart = ft.dwHighDateTime;

                                // FILETIME 是从1601年1月1日开始的100纳秒间隔
                                const uint64_t EPOCH_DIFFERENCE = 11644473600ULL * 10000000ULL;
                                uint64_t ns100 = ull.QuadPart - EPOCH_DIFFERENCE;

                                auto duration = std::chrono::nanoseconds(ns100 * 100);
                                return std::chrono::system_clock::time_point(std::chrono::duration_cast<std::chrono::system_clock::duration>(duration));
                            };

                            createdTime = convertFileTime(fileData.ftCreationTime);
                            modificationTime = convertFileTime(fileData.ftLastWriteTime);
                            accessTime = convertFileTime(fileData.ftLastAccessTime);
                        } else {
                            // 如果获取失败，使用当前时间
                            auto now = std::chrono::system_clock::now();
                            createdTime = now;
                            modificationTime = now;
                            accessTime = now;
                        }
                    } else {
                        // 文件不存在，使用当前时间
                        auto now = std::chrono::system_clock::now();
                        createdTime = now;
                        modificationTime = now;
                        accessTime = now;
                    }
                } catch (const std::exception& e) {
                    // 使用当前时间作为默认值
                    auto now = std::chrono::system_clock::now();
                    createdTime = now;
                    modificationTime = now;
                    accessTime = now;
                }

                actualDoc = core::SearchEngineService::Document(
                    id,
                    path,
                    FileAttributes(fileAttribs),
                    fileSize,
                    createdTime,        // 真实的创建时间
                    modificationTime,   // 真实的修改时间
                    accessTime,         // 真实的访问时间
                    std::make_optional(text)  // text content
                );

                // 注意：recordPtr 指向栈对象，不需要 delete
            }
        } catch (const std::exception& e) {
            qDebug() << "Failed to construct Document from path:" << QString::fromStdString(path.string()) << "Error:" << e.what();
            return;
        }
    }

    // 使用advancedChecker进行高级检查
    if (!advancedChecker_.check(actualDoc)) {
        return;
    }

    // 检查是否匹配搜索条件
    if (!matchesSearchCriteria(path, text)) {
        return;
    }

    // 保存检索结果
    saveSearchResult(path, text, actualDoc);
}

bool IncrementalSearchProcessor::matchesSearchCriteria(const std::filesystem::path& filePath,
                                                      const std::u8string& content) {
    
    // 检查全文匹配
    if (fullTextMatcher_.has_value()) {
        if (!fullTextMatcher_->matchOnce(content.c_str())) {
            return false;
        }
    }

    // 检查全文排除
    if (fullTextExcludeMatcher_.has_value()) {
        if (fullTextExcludeMatcher_->matchOnce(content.c_str())) {
            return false;
        }
    }

    return true;
}



void IncrementalSearchProcessor::saveSearchResult(const std::filesystem::path& filePath,
                                                 const std::u8string& content,
                                                 const core::SearchEngineService::Document& doc) {
    try {
        // 从doc获取文件信息
        auto path = doc.name();
        auto name = path.filename();
        auto dir = path.parent_path();

        // 文件路径和文件名高亮
        auto fileDirHighlighted = doc.name().parent_path().u8string();
        auto fileNameHighlighted = doc.name().filename().u8string();
        if (filePathHighlighter_.has_value()) {
            fileDirHighlighted = filePathHighlighter_.value()
                .highlightAll(doc.name().parent_path().u8string())
                .text();
            fileNameHighlighted = filePathHighlighter_.value()
                .highlightAll(doc.name().filename().u8string())
                .text();
        }

        // 全文高亮
        QString fullTextHighlightedJsonStr = "[]";
        TextNormalizer textNormalizer;
        auto beautifyText = textNormalizer.normalize(content);
        if (fullTextHighlighter_.has_value()) {
            auto arry = fullTextHighlighter_.value().highlightAbstract(beautifyText);
            QJsonArray json;
            for (int i = 0; i < arry.size(); ++i) {
                json.append(QString::fromUtf8(reinterpret_cast<const char*>(arry[i].text().c_str())));
            }
            fullTextHighlightedJsonStr = QJsonDocument(json).toJson();
        }

        // 提取关键词（使用预构造的keywordExtractor_）
        auto keywords = keywordExtractor_.extractQuick(content);
        QJsonArray keywordJson;
        for (auto &keyword : keywords) {
            QJsonObject obj;
            obj.insert("keyword", QString::fromUtf8(reinterpret_cast<const char*>(keyword.keyword().c_str())));
            obj.insert("hit", keyword.count());
            keywordJson.append(obj);
        }
        auto keywordsJsonStr = QString(QJsonDocument(keywordJson).toJson());
        bool bviolation = ViolationProcessor::instance()->judge(path, content);
        // 计算MD5（如果不是目录）
        QString pathStr = QString::fromUtf8(reinterpret_cast<const char*>(path.u8string().c_str()));
        QString md5 = doc.attributes().isDirectory() ? "" : calculateFileMD5(pathStr);
        auto taskResult = taskResultTable_->getByUniqueKey(path.string(), md5.toLocal8Bit().constData());
        if (taskResult.has_value()) return;
        // 检查是否为删除的文件
        bool deletedFile = false;
        

        // 生成时间戳
        auto currentTime = std::time(nullptr);
        QString timeEpoch = QString::number(currentTime);
        while(true) {
            // 调用TaskResultTable的insert方法
            bool ret = taskResultTable_->insert(
                taskId_,                                    // task_id
                path.string(),                              // path (从doc获取)
                name.string(),                              // name (从doc获取)
                dir.string(),                               // dir (从doc获取)
                doc.size(),                                 // size (从doc获取)
                std::chrono::system_clock::to_time_t(doc.creationTime()),      // create_time (从doc获取)
                std::chrono::system_clock::to_time_t(doc.lastModificationTime()), // modify_time (从doc获取)
                std::chrono::system_clock::to_time_t(doc.lastAccessTime()),    // access_time (从doc获取)
                QString::fromUtf8(reinterpret_cast<const char*>(fileNameHighlighted.c_str())).toLocal8Bit().constData(), // name_highlighted
                fullTextHighlightedJsonStr.toLocal8Bit().constData(),          // text_highlighted
                QString::fromUtf8(reinterpret_cast<const char*>(fileDirHighlighted.c_str())).toLocal8Bit().constData(),  // dir_highlighted
                doc.attributes().isRegularFile(),          // is_regular (从doc获取)
                doc.attributes().isDirectory(),            // is_directory (从doc获取)
                doc.attributes().isHidden(),               // is_hidden (从doc获取)
                doc.attributes().isSystem(),               // is_system (从doc获取)
                doc.attributes().isReadOnly(),             // is_readonly (从doc获取)
                currentTime,                                // timestamp
                0,                                          // uploaded
                keywordsJsonStr.toLocal8Bit().constData(), // keywords (JSON格式)
                md5.toLocal8Bit().constData(),             // md5
                timeEpoch.toLocal8Bit().constData(),       // time_epoch
                (bviolation?1:0),                                          // violation (暂不支持违规检测)
                deletedFile ? 1 : 0,                       // is_deleted
                bviolation?ViolationProcessor::instance()->getViolationTags() : ""                                          // tags
            );
            if (ret && bviolation) {
                taskResult = taskResultTable_->getByUniqueKey(path.string(), md5.toLocal8Bit().constData());
                if (!taskResult.has_value()){
                  ret = false;
                } else {
                  if (uploadFileTable_.insert(md5.toStdString(),
                                                         path.string())) {
                    ret = ViolationProcessor::instance()->process(networkAccessManager_.get(), taskResult.value());
                  } else {
                    ret = false;
                  }
                }
              }
            if (ret) {
                break;
            } else {
              QThread::msleep(2000);
              SPDLOG_WARN("insert or process violation failed, retry");
            }
        }
    } catch (const std::exception& e) {
        qWarning() << "Error saving search result:" << e.what();
    }
}

bool IncrementalSearchProcessor::filterFile(const std::filesystem::path& path) {
    // 使用现有的shouldProcessFile方法进行文件过滤
    return shouldProcessFile(path);
}

bool IncrementalSearchProcessor::filterDoc(const core::SearchEngineService::Document& doc) {
    // 使用advancedChecker进行文档过滤
    return advancedChecker_.check(doc);
}

bool IncrementalSearchProcessor::RecoverFile(uint64_t inode, int indexId, int volumId, const std::string& path) {
    // 增量检索处理器不需要文件恢复功能，返回false
    return false;
}

std::string IncrementalSearchProcessor::ConstructNewFileName(const std::string& filename, uint64_t inode) {
    // 增量检索处理器不需要构造新文件名功能，返回原文件名
    return filename;
}

} // namespace app
} // namespace anywhere
