#include "NonNTFSSearchProcessor.h"
#include <QStandardPaths>
#include <QJsonArray>
#include <QJsonObject>
#include <QJsonDocument>
#include <QFile>
#include <QIODevice>
#include <QCryptographicHash>
#include <QStorageInfo>
#include <QThread>
#include <QDir>
#include <chrono>
#include <thread>
#include <sstream>
#include <algorithm>
#include <spdlog/spdlog.h>

#include <windows.h>
#include "core/ServiceRegistry.h"
#include "core/services/ConfigService.h"
#include "core/services/SearchEngineService.h"
#include "core/services/FileContentIndexService.h"
#include "core/services/EventBusService.h"
#include "Miscs.h"  // 包含各种ServiceToken定义
#include "Executors.h"  // 包含MainExecutorToken和IoExecutorToken
#include "parser/DefaultFileParseEngine.h"
#include "parser/DefaultFileFormatDetector.h"
#include "FileFormat.h"
#include "KeywordExtractor.h"
#include "TextNormalizer.h"
#include "FileExtCheckerImpl.h"
#include "TermsQueryMatchEngine.h"

namespace anywhere {
namespace app {

using core::ConfigServiceToken;
using core::EventBusServiceToken;
using core::ServiceRegistry;

// 计算文件MD5值的辅助函数
static QString calculateFileMD5(const QString &filePath) {
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) return QString();

    const qint64 bufferSize = 10 * 1024 * 1024; // 10MB缓冲区
    char *buffer = new char[bufferSize];
    QCryptographicHash hash(QCryptographicHash::Md5);

    while (!file.atEnd()) {
        qint64 bytesRead = file.read(buffer, bufferSize);
        hash.addData(buffer, bytesRead);
    }
    file.close();
    delete[] buffer;
    return hash.result().toHex();
}

NonNTFSSearchProcessor::NonNTFSSearchProcessor(const std::string& taskId)
    : taskId_(taskId)
    , running_(false)
    , db_((QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/system.db").toStdString())
    , uploadFileTable_(db_)
    , completionFuture_(completionPromise_.get_future())
    , producerFinished_(false)
    , consumerCount_(std::thread::hardware_concurrency() > 4 ?  4 : std::thread::hardware_concurrency())
    , maxQueueSize_(consumerCount_ * 2 > 4? 4:consumerCount_ * 2) {
    
    // 初始化数据库表
    taskResultTable_ = std::make_shared<TaskResultTable>(db_);
    taskTable_ = std::make_shared<TaskTable>(db_);
    
    // 获取服务依赖（临时使用，后续会在updateSearchParameters中更新）
    auto tempConfigService = ServiceRegistry::instance()->getService<ConfigServiceToken>();
    configService_ = tempConfigService;
    
    auto eventBusService = ServiceRegistry::instance()->getService<EventBusServiceToken>();
    auto mainThreadExecutor = ServiceRegistry::instance()->getService<MainExecutorToken>();
    auto ioExecutor = ServiceRegistry::instance()->getService<IoExecutorToken>();
    
    // 创建搜索引擎服务
    searchEngineService_ = std::make_shared<core::SearchEngineService>(
        configService_, eventBusService, mainThreadExecutor, ioExecutor);
    
    // 创建文件解析引擎
    std::filesystem::path currentLibPath;
    auto tmpDir = std::filesystem::temp_directory_path();
    auto resourceReader = ServiceRegistry::instance()->getService<ResourceReaderToken>();
    
    auto defaultFileParseEngine = std::make_shared<parser::DefaultFileParseEngine>(
        tmpDir, currentLibPath, resourceReader,
        configService_->enabledFileFormats(),
        configService_->maxCharactorsPerFile());

    defaultFileParseEngine->setFileExtChecker(std::make_shared<FileExtCheckerImpl>());

    // 创建文件格式检测器
    fileFormatDetector_ = std::make_shared<parser::DefaultFileFormatDetector>(tmpDir, resourceReader);

    // 创建文件内容索引服务
    fileContentIndexService_ = std::make_shared<core::FileContentIndexService>(
        configService_, searchEngineService_, defaultFileParseEngine,
        eventBusService, mainThreadExecutor, ioExecutor);

    // 将自己设置为FileContentIndexService的docProcessor
    // 注意：这里不能直接使用shared_from_this()，因为对象还在构造中
    // 需要在start()方法中设置

    // 初始化驱动器列表
    initializeDrivers();

    // 初始化驱动器检查时间
    lastDriverCheck_ = std::chrono::steady_clock::now();

    // 初始化临时数据库
    initializeTempDatabase();
}

NonNTFSSearchProcessor::~NonNTFSSearchProcessor() {
    stop();
    cleanupTempDatabase();
}

void NonNTFSSearchProcessor::start() {
    if (running_.load()) {
        return; // 已经在运行
    }

    spdlog::info("Starting incremental search processor for task: {}", taskId_);

    // 将自己设置为FileContentIndexService的docProcessor
    if (fileContentIndexService_) {
        fileContentIndexService_->setDocProcessor(shared_from_this());
    }

    running_.store(true);
    processingThread_ = std::thread(&NonNTFSSearchProcessor::processLoop, this);
}

void NonNTFSSearchProcessor::stop() {
    if (!running_.load()) {
        return; // 没有在运行
    }

    spdlog::info("Stopping NonNTFS search processor for task: {}", taskId_);

    running_.store(false);

    // 停止生产者消费者模式
    stopProducerConsumerMode();

    if (processingThread_.joinable()) {
        processingThread_.join();
    }

    // 清除docProcessor设置
    if (fileContentIndexService_) {
        fileContentIndexService_->setDocProcessor(nullptr);
    }

    // 设置完成信号
    try {
        completionPromise_.set_value();
    } catch (const std::future_error&) {
        // Promise 已经被设置过了，忽略错误
    }
}

void NonNTFSSearchProcessor::updateSearchParameters(
    const FilePathFilter& pathFilter,
    const FileExtFilter& extFilter,
    std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fileNameMatcher,
    std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fileNameExcludeMatcher,
    std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fullTextMatcher,
    std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fullTextExcludeMatcher,
    std::optional<Highlighter>&& filePathHighlighter,
    std::optional<Highlighter>&& fullTextHighlighter,
    std::shared_ptr<core::ConfigService> configService,
    const AdvancedChecker& advancedChecker,
    const Query* parsedFullTextQuery) {

    // 更新过滤器
    filePathFilter_ = pathFilter;
    fileExtFilter_ = extFilter;

    // 更新匹配器（使用移动语义）
    fileNameMatcher_ = std::move(fileNameMatcher);
    fileNameExcludeMatcher_ = std::move(fileNameExcludeMatcher);
    fullTextMatcher_ = std::move(fullTextMatcher);
    fullTextExcludeMatcher_ = std::move(fullTextExcludeMatcher);

    // 更新高亮器
    filePathHighlighter_ = std::move(filePathHighlighter);
    fullTextHighlighter_ = std::move(fullTextHighlighter);

    // 更新配置和检查器
    configService_ = configService;
    advancedChecker_ = advancedChecker;

    // 保存查询对象的副本（如果提供）
    if (parsedFullTextQuery) {
        // 创建查询对象的副本
        savedParsedFullTextQuery_ = parsedFullTextQuery->clone();
    } else {
        savedParsedFullTextQuery_.reset();
    }

    // 构造 KeywordExtractor
    std::vector<std::u8string> terms;
    if (savedParsedFullTextQuery_) {
        auto termsQuery = dynamic_cast<const TermsQuery*>(savedParsedFullTextQuery_.get());
        if (termsQuery) {
            for (auto& term : termsQuery->terms()) {
                terms.push_back(term.term());
            }
        }
    }

    // 获取大小写敏感设置
    keywordExtractor_ = KeywordExtractor(terms, true);

    spdlog::info("Updated search parameters for task: {}", taskId_);
}

void NonNTFSSearchProcessor::processLoop() {
    spdlog::info("Starting NonNTFS search processing for task: {}", taskId_);
    spdlog::info("Using {} consumer threads with max queue size {}", consumerCount_, maxQueueSize_);

    try {
        // 启动生产者消费者模式
        startProducerConsumerMode();

        spdlog::info("NonNTFS search processing completed for task: {}", taskId_);

        // 处理结束后清空临时数据库
        cleanupTempDatabase();

    } catch (const std::exception& e) {
        spdlog::error("Error in NonNTFS search processing: {}", e.what());
    }

    // 设置完成信号
    try {
        completionPromise_.set_value();
    } catch (const std::future_error&) {
        // Promise 已经被设置过了，忽略错误
    }
}


void NonNTFSSearchProcessor::processValidatedFile(const FileRecord& record) {
    // 此方法假设文件已经通过了所有验证检查
    // 直接进行文件索引处理

    SPDLOG_DEBUG("Processing file: {}", record.filePath.string());

    try {
        // 由于record.filePath已经是std::filesystem::path，不需要编码转换
        if (std::filesystem::exists(record.filePath)) {
            CancellationToken token;
            // 生成一个DocId（可以使用文件路径的哈希值）
            DocId docId = std::hash<std::string>{}(record.filePath.string());
            fileContentIndexService_->indexDocumentSync(docId, record.filePath, true, (void*)(&record), token, [this]() {
                if (!savedParsedFullTextQuery_) {
                    return false;
                }
                if (collectCnt_ > configService_->maxHits()) {
                    return false;
                }
                return true;
            });
        } else {
            SPDLOG_WARN("File not found: {}", record.filePath.string());
        }
    } catch (const std::filesystem::filesystem_error& e) {
        spdlog::debug("Filesystem error processing file {}: {} - File may be on a removed drive",
                     record.filePath.string(), e.what());
        // 文件可能在已移除的驱动器上，这是正常情况，不需要警告
    } catch (const std::exception& e) {
        spdlog::warn("Error processing file change for {}: {}", record.filePath.string(), e.what());
    }
    // 记录到已处理列表中（避免重复消费）
    addProcessedFile(record.filePath);
}



bool NonNTFSSearchProcessor::shouldProcessFile(const std::filesystem::path& filePath) {
    if (collectCnt_ > configService_->maxHits()) {
      return false;
    }
    // 检查文件路径过滤器
    // 确保正确处理UTF-8编码的路径
    QString pathStr;
    try {
        // 在Windows上，从宽字符路径转换为QString
        pathStr = QString::fromStdWString(filePath.lexically_normal().generic_wstring());
    } catch (const std::exception& e) {
        spdlog::debug("Failed to convert file path to QString: {}", e.what());
        return false;
    }
    if (!filePathFilter_.match(pathStr)) {
        return false;
    }
    
    // 使用fileFormatDetector_检测文件格式
    QString ext;
    try {
        auto format = fileFormatDetector_->detectFormat(filePath, CancellationToken());
        ext = QString::fromStdString(getFileFormatName(format));
    } catch (const std::exception &e) {
        spdlog::debug("Failed to detectFormat file: {}", e.what());
        return false;
    }
    // 检查文件路径和检测到的格式扩展名
    if (!fileExtFilter_.match(pathStr) && !fileExtFilter_.match(ext)) {
        return false;
    }
    // 检查文件名匹配
    if (fileNameMatcher_.has_value()) {
        std::u8string fileName = filePath.filename().u8string();
        if (!fileNameMatcher_->matchOnce(fileName.c_str())) {
            return false;
        }
    }

    // 检查文件名排除
    if (fileNameExcludeMatcher_.has_value()) {
        std::u8string fileName = filePath.filename().u8string();
        if (fileNameExcludeMatcher_->matchOnce(fileName.c_str())) {
            return false;
        }
    }

    return true;
}

void NonNTFSSearchProcessor::onDocIndexed(DocId id, const std::filesystem::path& path,
                                            const std::u8string& text,
                                            std::optional<core::SearchEngineService::Document> doc,
                                            void* context) {
    // 如果doc为空，使用path构造一个Document
    if (collectCnt_ > configService_->maxHits()) {
      return;
    }
    core::SearchEngineService::Document actualDoc;
    if (doc.has_value()) {
        actualDoc = doc.value();
    } else {
        // 使用record中的文件信息构造Document
        try {
            // 从context中获取record信息
            auto* recordPtr = static_cast<FileRecord*>(context);
            if (!recordPtr) {
                spdlog::debug("Context is null, cannot get file info from record");
                // 使用默认值
                auto currentTime = std::chrono::system_clock::now();
                actualDoc = core::SearchEngineService::Document(
                    id, path,
                    FileAttributes(FileAttributes::Attributes::NORMAL),
                    0, currentTime, currentTime, currentTime,
                    std::make_optional(text)
                );
            } else {
                // 获取真实的文件系统信息 (Windows)
                uint64_t fileSize = 0;
                std::chrono::system_clock::time_point createdTime;
                std::chrono::system_clock::time_point modificationTime;
                std::chrono::system_clock::time_point accessTime;

                try {
                    // 获取文件大小
                    if (std::filesystem::exists(path)) {
                        fileSize = std::filesystem::file_size(path);

                        // Windows 文件时间获取
                        WIN32_FILE_ATTRIBUTE_DATA fileData;
                        if (GetFileAttributesExA(path.string().c_str(), GetFileExInfoStandard, &fileData)) {
                            // 转换 FILETIME 到 system_clock
                            auto convertFileTime = [](const FILETIME& ft) -> std::chrono::system_clock::time_point {
                                ULARGE_INTEGER ull;
                                ull.LowPart = ft.dwLowDateTime;
                                ull.HighPart = ft.dwHighDateTime;

                                // FILETIME 是从1601年1月1日开始的100纳秒间隔
                                const uint64_t EPOCH_DIFFERENCE = 11644473600ULL * 10000000ULL;
                                uint64_t ns100 = ull.QuadPart - EPOCH_DIFFERENCE;

                                auto duration = std::chrono::nanoseconds(ns100 * 100);
                                return std::chrono::system_clock::time_point(std::chrono::duration_cast<std::chrono::system_clock::duration>(duration));
                            };

                            createdTime = convertFileTime(fileData.ftCreationTime);
                            modificationTime = convertFileTime(fileData.ftLastWriteTime);
                            accessTime = convertFileTime(fileData.ftLastAccessTime);
                        } else {
                            // 如果获取失败，使用当前时间
                            auto now = std::chrono::system_clock::now();
                            createdTime = now;
                            modificationTime = now;
                            accessTime = now;
                        }
                    } else {
                        // 文件不存在，使用当前时间
                        auto now = std::chrono::system_clock::now();
                        createdTime = now;
                        modificationTime = now;
                        accessTime = now;
                    }
                } catch (const std::exception& e) {
                    spdlog::debug("Failed to get file system info for {}: {}", path.string(), e.what());
                    // 使用当前时间作为默认值
                    auto now = std::chrono::system_clock::now();
                    createdTime = now;
                    modificationTime = now;
                    accessTime = now;
                }

                // 设置为普通文件属性
                FileAttributes::Attributes fileAttribs = FileAttributes::Attributes::NORMAL;

                actualDoc = core::SearchEngineService::Document(
                    id,
                    path,
                    FileAttributes(fileAttribs),
                    fileSize,
                    createdTime,        // 真实的创建时间
                    modificationTime,   // 真实的修改时间
                    accessTime,         // 真实的访问时间
                    std::make_optional(text)  // text content
                );

                // 注意：recordPtr 指向栈对象，不需要 delete
            }
        } catch (const std::exception& e) {
            spdlog::debug("Failed to construct Document from path: {} Error: {}", path.string(), e.what());
            return;
        }
    }

    // 使用advancedChecker进行高级检查
    if (!advancedChecker_.check(actualDoc)) {
        return;
    }

    // 检查是否匹配搜索条件
    if (!matchesSearchCriteria(path, text)) {
        return;
    }

    // 保存检索结果
    saveSearchResult(path, text, actualDoc);
}

bool NonNTFSSearchProcessor::matchesSearchCriteria(const std::filesystem::path& filePath,
                                                      const std::u8string& content) {
    
    // 检查全文匹配
    if (fullTextMatcher_.has_value()) {
        if (!fullTextMatcher_->matchOnce(content.c_str())) {
            return false;
        }
    }

    // 检查全文排除
    if (fullTextExcludeMatcher_.has_value()) {
        if (fullTextExcludeMatcher_->matchOnce(content.c_str())) {
            return false;
        }
    }

    return true;
}



void NonNTFSSearchProcessor::saveSearchResult(const std::filesystem::path& filePath,
                                                 const std::u8string& content,
                                                 const core::SearchEngineService::Document& doc) {
    {
          std::lock_guard<std::mutex> lock(collectCntMutex_);
          ++collectCnt_;
    }
    try {
        // 从doc获取文件信息
        auto path = doc.name();
        auto name = path.filename();
        auto dir = path.parent_path();

        // 文件路径和文件名高亮
        auto fileDirHighlighted = doc.name().parent_path().u8string();
        auto fileNameHighlighted = doc.name().filename().u8string();
        if (filePathHighlighter_.has_value()) {
            fileDirHighlighted = filePathHighlighter_.value()
                .highlightAll(doc.name().parent_path().u8string())
                .text();
            fileNameHighlighted = filePathHighlighter_.value()
                .highlightAll(doc.name().filename().u8string())
                .text();
        }

        // 全文高亮
        QString fullTextHighlightedJsonStr = "[]";
        TextNormalizer textNormalizer;
        auto beautifyText = textNormalizer.normalize(content);
        if (fullTextHighlighter_.has_value()) {
            auto arry = fullTextHighlighter_.value().highlightAbstract(beautifyText);
            QJsonArray json;
            for (int i = 0; i < arry.size(); ++i) {
                json.append(QString::fromUtf8(reinterpret_cast<const char*>(arry[i].text().c_str())));
            }
            fullTextHighlightedJsonStr = QJsonDocument(json).toJson();
        }

        // 提取关键词（使用预构造的keywordExtractor_）
        auto keywords = keywordExtractor_.extractQuick(content);
        QJsonArray keywordJson;
        for (auto &keyword : keywords) {
            QJsonObject obj;
            obj.insert("keyword", QString::fromUtf8(reinterpret_cast<const char*>(keyword.keyword().c_str())));
            obj.insert("hit", keyword.count());
            keywordJson.append(obj);
        }
        auto keywordsJsonStr = QString(QJsonDocument(keywordJson).toJson());
        bool bviolation = ViolationProcessor::instance()->judge(path, content);
        // 计算MD5（如果不是目录）
        QString pathStr = QString::fromUtf8(reinterpret_cast<const char*>(path.u8string().c_str()));
        QString md5 = doc.attributes().isDirectory() ? "" : calculateFileMD5(pathStr);
        auto taskResult = taskResultTable_->getByUniqueKey(path.string(), md5.toLocal8Bit().constData());
        if (taskResult.has_value()) return;
        // 检查是否为删除的文件
        bool deletedFile = false;
        

        // 生成时间戳
        auto currentTime = std::time(nullptr);
        QString timeEpoch = QString::number(currentTime);
        while(true) {
            // 调用TaskResultTable的insert方法
            bool ret = taskResultTable_->insert(
                taskId_,                                    // task_id
                path.string(),                              // path (从doc获取)
                name.string(),                              // name (从doc获取)
                dir.string(),                               // dir (从doc获取)
                doc.size(),                                 // size (从doc获取)
                std::chrono::system_clock::to_time_t(doc.creationTime()),      // create_time (从doc获取)
                std::chrono::system_clock::to_time_t(doc.lastModificationTime()), // modify_time (从doc获取)
                std::chrono::system_clock::to_time_t(doc.lastAccessTime()),    // access_time (从doc获取)
                QString::fromUtf8(reinterpret_cast<const char*>(fileNameHighlighted.c_str())).toLocal8Bit().constData(), // name_highlighted
                fullTextHighlightedJsonStr.toLocal8Bit().constData(),          // text_highlighted
                QString::fromUtf8(reinterpret_cast<const char*>(fileDirHighlighted.c_str())).toLocal8Bit().constData(),  // dir_highlighted
                doc.attributes().isRegularFile(),          // is_regular (从doc获取)
                doc.attributes().isDirectory(),            // is_directory (从doc获取)
                doc.attributes().isHidden(),               // is_hidden (从doc获取)
                doc.attributes().isSystem(),               // is_system (从doc获取)
                doc.attributes().isReadOnly(),             // is_readonly (从doc获取)
                currentTime,                                // timestamp
                0,                                          // uploaded
                keywordsJsonStr.toLocal8Bit().constData(), // keywords (JSON格式)
                md5.toLocal8Bit().constData(),             // md5
                timeEpoch.toLocal8Bit().constData(),       // time_epoch
                (bviolation?1:0),                                          // violation (暂不支持违规检测)
                deletedFile ? 1 : 0,                       // is_deleted
                bviolation?ViolationProcessor::instance()->getViolationTags() : ""                                          // tags
            );
            if (ret && bviolation) {
                taskResult = taskResultTable_->getByUniqueKey(path.string(), md5.toLocal8Bit().constData());
                if (!taskResult.has_value()){
                  ret = false;
                } else {
                  if (uploadFileTable_.insert(md5.toStdString(),
                                                         path.string())) {
                    ret = ViolationProcessor::instance()->process(networkAccessManager_.get(), taskResult.value());
                  } else {
                    ret = false;
                  }
                }
              }
            if (ret) {
                break;
            } else {
              QThread::msleep(2000);
              SPDLOG_WARN("insert or process violation failed, retry");
            }
        }
    } catch (const std::exception& e) {
        spdlog::warn("Error saving search result: {}", e.what());
    }
}

bool NonNTFSSearchProcessor::filterFile(const std::filesystem::path& path) {
    // 使用现有的shouldProcessFile方法进行文件过滤
    return shouldProcessFile(path);
}

bool NonNTFSSearchProcessor::filterDoc(const core::SearchEngineService::Document& doc) {
    // 使用advancedChecker进行文档过滤
    return advancedChecker_.check(doc);
}

bool NonNTFSSearchProcessor::RecoverFile(uint64_t inode, int indexId, int volumId, const std::string& path) {
    // 增量检索处理器不需要文件恢复功能，返回false
    return false;
}

std::string NonNTFSSearchProcessor::ConstructNewFileName(const std::string& filename, uint64_t inode) {
    // 增量检索处理器不需要构造新文件名功能，返回原文件名
    return filename;
}

void NonNTFSSearchProcessor::initializeDrivers() {
    std::lock_guard<std::mutex> lock(driversMutex_);

    if (!drivers_.empty()) {
        return; // 已经设置了驱动器
    }

    // 获取所有非NTFS和非FIXED的存储设备
    QList<QStorageInfo> list = QStorageInfo::mountedVolumes();
    for (auto &storageInfo : list) {
        // 获取驱动器类型
        UINT driveType = GetDriveTypeA(storageInfo.rootPath().toUtf8().constData());

        if (storageInfo.fileSystemType() != "NTFS" || driveType != DRIVE_FIXED) {
            auto rootPath = storageInfo.rootPath();
            rootPath.replace('/', '\\');
            std::filesystem::path driverPath(rootPath.toStdWString());
            drivers_.push_back(driverPath);
            // qDebug() << "Found driver:" << rootPath << "FileSystem:" << storageInfo.fileSystemType();
        }
    }

    // qDebug() << "Initialized" << drivers_.size() << "drivers (non-NTFS and non-FIXED)";
}

void NonNTFSSearchProcessor::setDrivers(const std::vector<std::filesystem::path>& drivers) {
    std::lock_guard<std::mutex> lock(driversMutex_);
    drivers_ = drivers;
    spdlog::info("Set {} custom drivers", drivers_.size());
}

std::vector<std::filesystem::path> NonNTFSSearchProcessor::getDrivers() const {
    std::lock_guard<std::mutex> lock(driversMutex_);
    return drivers_;
}

void NonNTFSSearchProcessor::waitForCompletion() {
    if (completionFuture_.valid()) {
        completionFuture_.wait();
    }
}

void NonNTFSSearchProcessor::traverseDrivers() {
    // 每5分钟检查一次驱动器可用性
    auto now = std::chrono::steady_clock::now();
    if (std::chrono::duration_cast<std::chrono::minutes>(now - lastDriverCheck_).count() >= 5) {
        checkAndRemoveUnavailableDrivers();
        lastDriverCheck_ = now;
    }

    std::lock_guard<std::mutex> lock(driversMutex_);

    for (const auto& driver : drivers_) {
        if (!running_.load()) {
            break; // 检查是否需要停止
        }

        // 检查驱动器是否仍然可用
        if (!isDriverAvailable(driver)) {
            spdlog::warn("Driver {} is no longer available, skipping...", driver.string());
            continue;
        }

        // qDebug() << "Traversing driver:" << QString::fromStdWString(driver.wstring());

        try {
            if (std::filesystem::exists(driver) && std::filesystem::is_directory(driver)) {
                traverseDirectory(driver);
            }
        } catch (const std::filesystem::filesystem_error& e) {
            spdlog::warn("Filesystem error traversing driver {}: {} - Driver may have been removed",
                        driver.string(), e.what());
            // 驱动器可能已被移除，继续处理下一个驱动器
            continue;
        } catch (const std::exception& e) {
            spdlog::warn("Error traversing driver {}: {}", driver.string(), e.what());
        }
    }
}

void NonNTFSSearchProcessor::traverseDirectory(const std::filesystem::path& directory, int depth) {
    // 检查深度限制，防止栈溢出
    if (depth > 20) {
        spdlog::warn("Directory depth limit (20) exceeded for: {}", directory.string());
        return;
    }

    try {
        // 在开始遍历前检查目录是否仍然存在
        if (!std::filesystem::exists(directory)) {
            spdlog::debug("Directory no longer exists: {}", directory.string());
            return;
        }

        std::filesystem::directory_iterator it(directory);
        std::filesystem::directory_iterator end;

        for (; it != end && running_.load(); ++it) {
            const auto& entry = *it;

            try {
                if (entry.is_directory()) {
                    // 递归遍历子目录，深度+1
                    traverseDirectory(entry.path(), depth + 1);
                } else if (entry.is_regular_file()) {
                    // 生产文件任务
                    auto filePath = entry.path();

                    // 先检查路径是否已处理
                    if (isFileProcessed(filePath)) {
                        continue;
                    }

                    // 检查文件是否应该被处理
                    if (!shouldProcessFile(filePath)) {
                        continue;
                    }

                    // 创建文件记录
                    FileRecord record;
                    record.filePath = filePath;  // 直接赋值filesystem::path，避免编码转换
                    record.changeType = 1; // Created
                    record.fileSize = entry.file_size();  // 下面的都是假数据，不要了
                    // record.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                    //     std::chrono::system_clock::now().time_since_epoch()).count();
                    // record.createdTime = std::time(nullptr);
                    // record.fileAttributes = 0; // 默认属性

                    // 将文件任务加入队列
                    {
                        std::unique_lock<std::mutex> lock(queueMutex_);
                        // 等待队列有空间
                        queueCondition_.wait(lock, [this] {
                            return fileQueue_.size() < maxQueueSize_ || !running_.load();
                        });

                        if (!running_.load()) {
                            break; // 停止生产
                        }

                        fileQueue_.emplace(filePath, record);
                    }
                    queueCondition_.notify_one(); // 通知消费者
                }
            } catch (const std::filesystem::filesystem_error& e) {
                // 忽略单个文件/目录的错误，继续处理其他文件
                spdlog::debug("Filesystem error for {}: {}", entry.path().string(), e.what());
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        spdlog::warn("Error accessing directory {}: {}", directory.string(), e.what());
    }
}

void NonNTFSSearchProcessor::initializeTempDatabase() {
    // 创建临时数据库路径
    auto dbname = QString::fromStdString(taskId_) + "_nonntfs.db";
    tmpDBPath_ = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/tmp/" + dbname;

    // 确保tmp目录存在
    QDir tmpDir(QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/tmp");
    if (!tmpDir.exists()) {
        tmpDir.mkpath(".");
    }

    // 创建临时数据库
    tempDb_ = std::make_unique<Database>(tmpDBPath_.toStdString());

    // 创建PathTable
    tempPathTable_ = std::make_shared<PathTable>(*tempDb_);

    // qDebug() << "Initialized temp database for task:" << QString::fromStdString(taskId_)
    //          << "at:" << tmpDBPath_;
}

void NonNTFSSearchProcessor::cleanupTempDatabase() {
    // 清理临时数据库
    tempPathTable_.reset();
    tempDb_.reset();

    // 删除临时数据库文件
    if (!tmpDBPath_.isEmpty() && QFile::exists(tmpDBPath_)) {
        if (QFile::remove(tmpDBPath_)) {
            spdlog::info("Removed temp database file: {}", tmpDBPath_.toStdString());
        } else {
            spdlog::warn("Failed to remove temp database file: {}", tmpDBPath_.toStdString());
        }
    }

    spdlog::info("Cleaned up temp database for task: {}", taskId_);
}

UINT NonNTFSSearchProcessor::getDriveType(const std::filesystem::path& filePath) const {
    auto rootPath = filePath.root_path().string();

    // 检查缓存
    {
        std::lock_guard<std::mutex> lock(driveTypeCacheMutex_);
        auto it = driveTypeCache_.find(rootPath);
        if (it != driveTypeCache_.end()) {
            return it->second;
        }
    }

    // 获取驱动器类型
    UINT driveType = GetDriveTypeA(rootPath.c_str());

    // 缓存结果
    {
        std::lock_guard<std::mutex> lock(driveTypeCacheMutex_);
        driveTypeCache_[rootPath] = driveType;
    }

    return driveType;
}

bool NonNTFSSearchProcessor::isFileProcessed(const std::filesystem::path& filePath) {
    // 检查文件所在驱动器类型
    UINT driveType = getDriveType(filePath);

    // 如果是非固定驱动器，不需要查库，直接返回false
    if (driveType != DRIVE_FIXED) {
        return false;
    }

    // 只有固定驱动器的文件才查询PathTable
    if (tempPathTable_) {
        return tempPathTable_->exists(filePath.string());
    }

    return false;
}

void NonNTFSSearchProcessor::addProcessedFile(const std::filesystem::path& filePath) {
    // 检查文件所在驱动器类型
    UINT driveType = getDriveType(filePath);

    // 只有非固定驱动器的文件才写入数据库
    if (driveType != DRIVE_FIXED) {
        return;
    }
    if (tempPathTable_) {
        tempPathTable_->insert(filePath.string());
    }
}

bool NonNTFSSearchProcessor::isDriverAvailable(const std::filesystem::path& driver) {
    try {
        // 检查驱动器路径是否存在
        if (!std::filesystem::exists(driver)) {
            return false;
        }

        // 检查是否为目录
        if (!std::filesystem::is_directory(driver)) {
            return false;
        }

        // 尝试访问驱动器（通过列举根目录来测试可访问性）
        std::filesystem::directory_iterator it(driver);
        // 如果能成功创建迭代器，说明驱动器可访问
        return true;

    } catch (const std::filesystem::filesystem_error& e) {
        // 文件系统错误通常表示驱动器不可访问或已被移除
        spdlog::debug("Driver availability check failed for {}: {}", driver.string(), e.what());
        return false;
    } catch (const std::exception& e) {
        // 其他异常也认为驱动器不可用
        spdlog::debug("Unexpected error checking driver availability for {}: {}",
                     driver.string(), e.what());
        return false;
    }
}

void NonNTFSSearchProcessor::checkAndRemoveUnavailableDrivers() {
    std::lock_guard<std::mutex> lock(driversMutex_);

    auto it = drivers_.begin();
    while (it != drivers_.end()) {
        if (!isDriverAvailable(*it)) {
            spdlog::warn("Removing unavailable driver: {}", it->string());
            it = drivers_.erase(it);
        } else {
            ++it;
        }
    }

    if (drivers_.empty()) {
        spdlog::warn("All drivers have been removed or are unavailable. Stopping processing.");
        // 所有驱动器都不可用，停止处理
        running_.store(false);
    }
}

void NonNTFSSearchProcessor::startProducerConsumerMode() {
    // 重置状态
    producerFinished_.store(false);

    // 启动消费者线程
    consumerThreads_.reserve(consumerCount_);
    for (size_t i = 0; i < consumerCount_; ++i) {
        consumerThreads_.emplace_back(&NonNTFSSearchProcessor::fileConsumer, this);
    }

    // 在当前线程中运行生产者
    fileProducer();

    // 标记生产者完成
    producerFinished_.store(true);
    queueCondition_.notify_all(); // 通知所有消费者

    // 等待所有消费者完成
    for (auto& thread : consumerThreads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    consumerThreads_.clear();
}

void NonNTFSSearchProcessor::stopProducerConsumerMode() {
    // 通知所有线程停止
    {
        std::lock_guard<std::mutex> lock(queueMutex_);
        producerFinished_.store(true);
    }
    queueCondition_.notify_all();

    // 等待所有消费者线程结束
    for (auto& thread : consumerThreads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    consumerThreads_.clear();

    // 清空队列
    std::lock_guard<std::mutex> lock(queueMutex_);
    while (!fileQueue_.empty()) {
        fileQueue_.pop();
    }
}

void NonNTFSSearchProcessor::fileProducer() {
    spdlog::info("File producer started");

    try {
        // 遍历所有驱动器
        traverseDrivers();
    } catch (const std::exception& e) {
        spdlog::error("Error in file producer: {}", e.what());
    }

    spdlog::info("File producer finished");
}

void NonNTFSSearchProcessor::fileConsumer() {
    spdlog::info("File consumer started");

    while (running_.load()) {
        FileTask task("", FileRecord{});
        bool hasTask = false;

        // 从队列中获取任务
        {
            std::unique_lock<std::mutex> lock(queueMutex_);
            queueCondition_.wait(lock, [this] {
                return !fileQueue_.empty() || producerFinished_.load() || !running_.load();
            });

            if (!running_.load()) {
                break;
            }

            if (!fileQueue_.empty()) {
                task = fileQueue_.front();
                fileQueue_.pop();
                hasTask = true;
            } else if (producerFinished_.load()) {
                // 生产者已完成且队列为空，退出
                break;
            }
        }

        // 通知生产者队列有空间了
        queueCondition_.notify_one();

        // 处理任务
        if (hasTask) {
            try {
                processValidatedFile(task.record);
            } catch (const std::exception& e) {
                spdlog::warn("Error processing file in consumer {}: {}",
                            task.record.filePath.string(), e.what());
            }
        }
    }

    spdlog::info("File consumer finished");
}

} // namespace app
} // namespace anywhere
