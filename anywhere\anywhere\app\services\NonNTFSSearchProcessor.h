#pragma once

#include <QString>
#include <QObject>
#include <QStorageInfo>
#include <atomic>
#include <thread>
#include <memory>
#include <filesystem>
#include <vector>
#include <set>
#include <future>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <ctime>
#include <unordered_map>
#include "database/Database.h"
#include "database/TaskResultTable.h"
#include "database/TaskTable.h"
#include "database/PathTable.h"
#include "core/services/FileContentIndexService.h"
#include "core/services/SearchEngineService.h"
#include "core/services/ConfigService.h"
#include "CancellationToken.h"
#include "FilePathFilter.h"
#include "FileExtFilter.h"
#include "mpm/AutoMpmEngine.h"
#include "TermsQueryMatchEngine.h"
#include "CheckerUtils.h"
#include "Highlighter.h"
#include "parser/FileFormatDetector.h"
#include "database/UploadFileTable.h"
#include "KeywordExtractor.h"
#include <QNetworkAccessManager>

namespace anywhere {
namespace app {

/**
 * 增量检索处理器
 * 负责持续监控file_change_log表中的文件变更，并进行增量检索处理
 * 独立运行，不依赖于BackgroundModel的生命周期
 * 实现DocProcessor接口，作为FileContentIndexService的文档处理器
 */
class NonNTFSSearchProcessor : public core::DocProcessor, public std::enable_shared_from_this<NonNTFSSearchProcessor> {
public:
    explicit NonNTFSSearchProcessor(const std::string& taskId);
    ~NonNTFSSearchProcessor();

    // 启动和停止增量检索
    void start();
    void stop();
    bool isRunning() const { return running_.load(); }

    // 等待执行完成
    void waitForCompletion();

    // 设置要扫描的驱动器
    void setDrivers(const std::vector<std::filesystem::path>& drivers);

    // 获取当前设置的驱动器
    std::vector<std::filesystem::path> getDrivers() const;

    // 更新搜索参数（当任务变化时调用）
    void updateSearchParameters(const FilePathFilter& pathFilter,
                               const FileExtFilter& extFilter,
                               std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fileNameMatcher,
                               std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fileNameExcludeMatcher,
                               std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fullTextMatcher,
                               std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fullTextExcludeMatcher,
                               std::optional<Highlighter>&& filePathHighlighter,
                               std::optional<Highlighter>&& fullTextHighlighter,
                               std::shared_ptr<core::ConfigService> configService,
                               const AdvancedChecker& advancedChecker,
                               const Query* parsedFullTextQuery = nullptr);

    // 设置网络访问管理器
    void setNetworkAccessManager(std::shared_ptr<QNetworkAccessManager> networkAccessManager) {
        networkAccessManager_ = networkAccessManager;
    }

private:
    // 主处理循环
    void processLoop();



    // 初始化驱动器列表
    void initializeDrivers();

    // 遍历驱动器下的所有文件
    void traverseDrivers();

    // 递归遍历目录（生产者）
    void traverseDirectory(const std::filesystem::path& directory, int depth = 0);

    // 生产者：扫描文件并加入队列
    void fileProducer();

    // 消费者：处理队列中的文件
    void fileConsumer();

    // 启动生产者消费者模式
    void startProducerConsumerMode();

    // 停止生产者消费者模式
    void stopProducerConsumerMode();

    // 检查驱动器是否仍然可用
    bool isDriverAvailable(const std::filesystem::path& driver);

    // 检查并移除不可用的驱动器
    void checkAndRemoveUnavailableDrivers();

    // 初始化临时数据库
    void initializeTempDatabase();

    // 清空并删除临时数据库
    void cleanupTempDatabase();

    // 检查文件路径是否已处理
    bool isFileProcessed(const std::filesystem::path& filePath);

    // 添加文件路径到已处理列表
    void addProcessedFile(const std::filesystem::path& filePath);

    // 文件过滤
    bool shouldProcessFile(const std::filesystem::path& filePath);

    // 检索匹配
    bool matchesSearchCriteria(const std::filesystem::path& filePath,
                              const std::u8string& content);



    // 保存检索结果
    void saveSearchResult(const std::filesystem::path& filePath,
                         const std::u8string& content,
                         const core::SearchEngineService::Document& doc);

    // DocProcessor接口实现
    void onDocIndexed(DocId id, const std::filesystem::path& path,
                     const std::u8string& text,
                     std::optional<core::SearchEngineService::Document> doc,
                     void* context) override;

    bool filterFile(const std::filesystem::path& path) override;
    bool filterDoc(const core::SearchEngineService::Document& doc) override;
    bool RecoverFile(uint64_t inode, int indexId, int volumId, const std::string& path) override;
    std::string ConstructNewFileName(const std::string& filename, uint64_t inode) override;

private:
    // 简化的文件记录结构
    struct FileRecord {
        std::string filePath;
        int changeType;
        uint64_t fileSize;
        int64_t timestamp;
        std::time_t createdTime;
        uint32_t fileAttributes;

        FileRecord() : changeType(0), fileSize(0), timestamp(0), createdTime(0), fileAttributes(0) {}
    };

    // 文件处理任务结构
    struct FileTask {
        std::filesystem::path filePath;
        FileRecord record;

        FileTask(const std::filesystem::path& path, const FileRecord& rec)
            : filePath(path), record(rec) {}
    };

    // 处理已验证的文件（内部使用，跳过重复检查）
    void processValidatedFile(const FileRecord& record);

    // 获取驱动器类型（带缓存）
    UINT getDriveType(const std::filesystem::path& filePath) const;

    std::string taskId_;
    std::atomic<bool> running_;
    std::thread processingThread_;
    std::promise<void> completionPromise_;
    std::future<void> completionFuture_;

    // 生产者消费者相关
    std::queue<FileTask> fileQueue_;
    std::mutex queueMutex_;
    std::condition_variable queueCondition_;
    std::atomic<bool> producerFinished_;
    std::vector<std::thread> consumerThreads_;
    const size_t consumerCount_;
    const size_t maxQueueSize_;

    // 驱动器列表
    std::vector<std::filesystem::path> drivers_;
    mutable std::mutex driversMutex_;

    // 驱动器可用性检查
    std::chrono::steady_clock::time_point lastDriverCheck_;
    
    // 数据库相关
    Database db_;
    std::shared_ptr<TaskResultTable> taskResultTable_;
    std::shared_ptr<TaskTable> taskTable_;

    // 临时数据库相关
    std::unique_ptr<Database> tempDb_;
    std::shared_ptr<PathTable> tempPathTable_;
    QString tmpDBPath_;
    
    // 服务依赖
    std::shared_ptr<core::FileContentIndexService> fileContentIndexService_;
    std::shared_ptr<core::SearchEngineService> searchEngineService_;
    std::shared_ptr<core::ConfigService> configService_;
    std::shared_ptr<parser::FileFormatDetector> fileFormatDetector_;
    
    // 搜索参数
    FilePathFilter filePathFilter_;
    FileExtFilter fileExtFilter_;

    // 查询匹配器
    std::optional<TermsQueryMatcher<AutoMpmEngine>> fileNameMatcher_;
    std::optional<TermsQueryMatcher<AutoMpmEngine>> fileNameExcludeMatcher_;
    std::optional<TermsQueryMatcher<AutoMpmEngine>> fullTextMatcher_;
    std::optional<TermsQueryMatcher<AutoMpmEngine>> fullTextExcludeMatcher_;

    // 高亮器
    std::optional<Highlighter> filePathHighlighter_;
    std::optional<Highlighter> fullTextHighlighter_;

    // 高级检查器
    AdvancedChecker advancedChecker_;

    // 关键词提取器
    KeywordExtractor keywordExtractor_;

    // 保存的查询对象副本（用于关键词提取）
    std::unique_ptr<Query> savedParsedFullTextQuery_;

    // 网络和上传相关
    UploadFileTable uploadFileTable_;
    std::shared_ptr<QNetworkAccessManager> networkAccessManager_;

    // 处理统计
    // std::atomic<size_t> processedCount_;
    // std::atomic<size_t> matchedCount_;

    // 收集计数相关
    std::atomic<size_t> collectCnt_{0};
    std::mutex collectCntMutex_;

    // 驱动器类型缓存
    mutable std::unordered_map<std::string, UINT> driveTypeCache_;
    mutable std::mutex driveTypeCacheMutex_;

    // 辅助函数：处理路径编码转换
    std::filesystem::path convertToFilesystemPath(const std::string& pathStr) const;
};

} // namespace app
} // namespace anywhere
