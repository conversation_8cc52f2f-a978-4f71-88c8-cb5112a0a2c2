#include "TaskManager.h"
#include "TaskScheduler.h"

#include <chrono>
#include <QStandardPaths>
#include "app/windows/QTrayIcon.h"
#include <QFileInfo>

namespace anywhere {
namespace app {

// Initialize static member
std::unique_ptr<TaskManager> TaskManager::instance_;

TaskManager::TaskManager(): QObject(nullptr),
    db_((QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/system.db").toStdString()),
    taskTable_(db_),
    systemInfoTable_(db_),
    taskResultTable_(db_),
    taskHisResultTable_(db_),
    policyTable_(db_),
    uploadFileTable_(db_),
    networkService_(core::ServiceRegistry::instance()->getService<NetworkServiceToken>()),
    running_(false) ,threadPool_(this){
    if (!networkService_) {
        qWarning() << "Failed to get NetworkService";
    }
}

TaskManager::~TaskManager() {
    stop();
}

void TaskManager::start() {
    if (running_) return;
    running_ = true;
    threadPool_.start(new Worker(this, &TaskManager::createHostThread));
    threadPool_.start(new Worker(this, &TaskManager::scanTasksThread));
    threadPool_.start(new Worker(this, &TaskManager::executeTasksThread));
    threadPool_.start(new Worker(this, &TaskManager::checkTasksThread));
    threadPool_.start(new Worker(this, &TaskManager::uploadTaskResultsThread));
    threadPool_.start(new Worker(this, &TaskManager::uploadTaskHisResultsThread));
    threadPool_.start(new Worker(this, &TaskManager::checkLockScreenThread));
    threadPool_.start(new Worker(this, &TaskManager::uploadFilesThread));
}

void TaskManager::stop() {
    if (!running_) return;
    running_ = false;
    schedulerCV_.notify_all();
    
    {
        std::lock_guard<std::mutex> lock(schedulerMutex_);
        std::vector<std::shared_ptr<TaskScheduler>> schedulers;
        while (!schedulers_.empty()) {
            auto scheduler = schedulers_.top();
            scheduler->stop();
            schedulers_.pop();
            schedulers.push_back(scheduler);
        }
    }
    
    threadPool_.waitForDone();
}


void TaskManager::checkTasksThread() {
    QThread::sleep(10);
    auto networkManager = std::make_shared<QNetworkAccessManager>(nullptr);
    while (running_) {
        try {
            // Get all tasks from system info table
            auto allTasksOpt = systemInfoTable_.get("all_tasks");
            std::vector<std::string> taskEntries;
            if (allTasksOpt && !allTasksOpt->value.empty()) {
                std::string allTasks = allTasksOpt->value;
                size_t pos = 0;
                while ((pos = allTasks.find(',')) != std::string::npos) {
                    taskEntries.push_back(allTasks.substr(0, pos));
                    allTasks.erase(0, pos + 1);
                }
                if (!allTasks.empty()) {
                    taskEntries.push_back(allTasks);
                }
                taskEntriesSize_ = taskEntries.size();
                // 简化逻辑：只处理第一个任务
                if (!taskEntries.empty()) {
                    const std::string& firstTaskId = taskEntries[0];

                    // 如果第一个任务在表中不存在
                    if (!taskTable_.exists(firstTaskId)) {
                        // 将表中所有非cancelled状态的任务设置为Cancelled
                        taskTable_.updateAllNonCancelledToCancelled();

                        // 然后执行其他逻辑：从服务器获取第一个任务
                        if (running_ && networkService_) {
                            auto taskOpt = networkService_->getTaskByID(QString::fromStdString(firstTaskId), networkManager.get());
                            if (taskOpt) {
                                taskTable_.insertOrUpdate(taskOpt.value());
                                policyTable_.insertOrUpdate(taskOpt.value().policy);
                                Q_EMIT QTrayIcon::instance()->newTaskSignal();
                            } else {
                                qWarning() << "Failed to fetch task:" << QString::fromStdString(firstTaskId);
                            }
                        }
                    }
                    // 如果第一个任务在表中已存在，则忽略
                } else {
                    taskTable_.updateAllNonCancelledToCancelled();
                }
                firstRun_ = false;
                QThread::sleep(1);
            }
            // taskTable_.deleteTaskExclude(taskEntries);
        } catch (...) {
            // Log error
        }
        QThread::sleep(3);
    }
}

bool TaskManager::TaskPriorityCompare::operator()(
    const std::shared_ptr<TaskScheduler>& lhs, 
    const std::shared_ptr<TaskScheduler>& rhs) {
    if (lhs->priority() == rhs->priority()) {
        return lhs->taskId() < rhs->taskId();
    }
    return lhs->priority() < rhs->priority();
}

void TaskManager::scanTasksThread() {
    // auto networkManager = std::make_shared<QNetworkAccessManager>(nullptr);
    QThread::sleep(10);
    while (running_) {
        if (firstRun_) {
            QThread::sleep(2);
            continue;
        }
        try {
            std::lock_guard<std::mutex> lock(schedulerMutex_);
            auto tasks = taskTable_.getRunningTasks();
            for (const auto& task : tasks) {
                bool exists = false;
                auto tempQueue = schedulers_;
                if (!tempQueue.empty() && tempQueue.top()->taskId() != task.taskId) {
                    tempQueue.top()->stop();
                } else if (tempQueue.empty()) {
                    auto scheduler = std::make_shared<TaskScheduler>(task, taskTable_, policyTable_);
                    if (scheduler->shouldExecute()) {
                        schedulers_.push(scheduler);
                        schedulerCV_.notify_one();
                    }
                }
            }
            if (tasks.size() == 0 && !schedulers_.empty()) {
              schedulers_.top()->stop();
            }
        } catch (...) {
            // Log error
        }
        
        QThread::sleep(2);
    }
}


void TaskManager::createHostThread() {
    auto networkManager = std::make_shared<QNetworkAccessManager>(nullptr);
    while (running_) {
        bool ret = networkService_->createHost(networkManager.get());
        if (!ret) {
            QThread::sleep(2);
            continue;
        }
        auto hostId = systemInfoTable_.getValue("host_id");
        if (hostId && !hostId.value().empty()) {
            return;
        }
        QThread::sleep(2);
    }
}

void TaskManager::executeTasksThread() {
    auto networkManager = std::make_shared<QNetworkAccessManager>(nullptr);
    QThread::sleep(10);
    while (running_) {
        std::shared_ptr<TaskScheduler> scheduler;
        {
            std::unique_lock<std::mutex> lock(schedulerMutex_);
            schedulerCV_.wait(lock, [this] { 
                return !running_ || !schedulers_.empty(); 
            });

            if (!running_) break;
            auto value = systemInfoTable_.getValue("lock_screen");
            if (value.has_value() && *value != "0") {
              QThread::sleep(2);
              continue;
            }
            scheduler = schedulers_.top();
            
            
            scheduler->setNetworkAccessManager(networkManager);
            scheduler->execute();
            schedulers_.pop();
        }
        // cleanupScheduler(scheduler, true);
    }
}

void TaskManager::cleanupScheduler(std::shared_ptr<TaskScheduler> scheduler, bool success) {
    if (!success) {
        std::lock_guard<std::mutex> lock(schedulerMutex_);
        schedulers_.push(scheduler);
    }
}

void TaskManager::uploadTaskHisResultsThread() {
    auto networkManager = std::make_shared<QNetworkAccessManager>(nullptr);
    while (running_) {
        try {
            auto unuploadedResults = taskHisResultTable_.getByUploaded(0);
            for (const auto& result : unuploadedResults) {
                if (!running_) break;
                
                if (networkService_) {
                    bool success = networkService_->sendTaskHisResult(result, networkManager.get());
                    if (success) {
                        taskHisResultTable_.updateUploaded(result.id, true);
                    }
                    QThread::msleep(500);
                }
            }
        } catch (...) {
            qWarning() << "Exception in uploadTaskHisResultsThread";
        }
        
        QThread::sleep(uploadInterval_.count());
    }
}

void TaskManager::checkLockScreenThread() {
    auto networkManager = std::make_shared<QNetworkAccessManager>(nullptr);
    while (running_) {
        try {
            auto lockScreenInfo = systemInfoTable_.get("lock_screen");
            if (lockScreenInfo && lockScreenInfo->description == "NotNoticed") {
                if (lockScreenInfo->value == "0") {
                    if (networkService_ && networkService_->unlock(networkManager.get())) {
                        systemInfoTable_.updateDescription("lock_screen", "Noticed");
                    }
                } else if (lockScreenInfo->value == "1") {
                    if (networkService_ && networkService_->lock(networkManager.get())) {
                        systemInfoTable_.updateDescription("lock_screen", "Noticed");
                    }
                }
            }
        } catch (...) {
            qWarning() << "Exception in checkLockScreenThread";
        }
        QThread::sleep(2);
    }
}

void TaskManager::uploadTaskResultsThread() {
    auto networkManager = std::make_shared<QNetworkAccessManager>(nullptr);
    while (running_) {
        try {
            // Get unuploaded task results
            // TaskResultTable taskResultTable(db_);
            auto unuploadedResults = taskResultTable_.getByUploaded(0);
            for (const auto& result : unuploadedResults) {
                if (!running_) break;
                // Send result via NetworkService
                if (networkService_) {
                    bool success = networkService_->sendTaskResult(result, networkManager.get());
                    if (success) {
                        // Update uploaded status
                        taskResultTable_.updateUploaded(result.id, true);
                    }
                }
                QThread::msleep(500);
            }
        } catch (...) {
            // Log error
        }
        
        QThread::sleep(uploadInterval_.count());
    }
}

void TaskManager::uploadFilesThread() {
    auto networkManager = std::make_shared<QNetworkAccessManager>(nullptr);
    QThread::sleep(10);
    while (running_) {
        try {
            auto uploadedFiles = uploadFileTable_.getByStatus(UploadFileTable::FileStatus::UPLOADED);
            for (const auto& file : uploadedFiles) {
                if (!running_) break;
                bool ret = networkService_->noticeFileUploaded(
                    file, networkManager.get());
                if (ret) {
                  uploadFileTable_.updateStatusAndUrl(
                      file.md5, UploadFileTable::FileStatus::UPLOADED_NOTICED, file.url);
                }
            }
            // Get pending files
            auto pendingFiles = uploadFileTable_.getByStatus(UploadFileTable::FileStatus::PENDING);
            for (const auto& file : pendingFiles) {
                if (!running_) break;
                QFileInfo fileInfo(QString::fromLocal8Bit(file.file_path.c_str()));
                if (!fileInfo.exists() || fileInfo.isDir()) {
                    uploadFileTable_.updateStatusAndUrl(file.md5, UploadFileTable::FileStatus::NOT_EXIST, "");
                    continue;
                }
                if (networkService_) {
                    // Make a copy of file to pass to uploadFile
                    auto fileToUpload = file;
                    bool success = networkService_->uploadFile(fileToUpload);
                    if (success) {
                        // Update status to UPLOADED and set URL
                        uploadFileTable_.updateStatusAndUrl(
                            file.md5, 
                            UploadFileTable::UPLOADED, 
                            fileToUpload.url
                        );
                    }
                }
                QThread::msleep(500);
            }
        } catch (...) {
            qWarning() << "Exception in uploadFilesThread";
        }
        
        QThread::sleep(uploadInterval_.count());
    }
}

void TaskManager::addAppendDriver(const std::string& driver) {
    std::lock_guard<std::mutex> lock(appendDriversMutex_);
    appendDrivers.insert(driver);
    qDebug() << "Added driver to appendDrivers:" << QString::fromStdString(driver);
}

void TaskManager::removeAppendDriver(const std::string& driver) {
    std::lock_guard<std::mutex> lock(appendDriversMutex_);
    appendDrivers.erase(driver);
    qDebug() << "Removed driver from appendDrivers:" << QString::fromStdString(driver);
}

std::set<std::string> TaskManager::getAppendDrivers() const {
    std::lock_guard<std::mutex> lock(appendDriversMutex_);
    return appendDrivers;  // 返回副本
}

void TaskManager::clearAppendDrivers() {
    std::lock_guard<std::mutex> lock(appendDriversMutex_);
    appendDrivers.clear();
    qDebug() << "Cleared all appendDrivers";
}

uint32_t TaskManager::getTaskEntriesSize() const {
    return taskEntriesSize_;
}

}
}

