#include "TaskScheduler.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonParseError>
#include "ViolationProcessor.h"
#include "IncrementalSearchManager.h"

#include "TaskManager.h"
#include <sstream>

namespace anywhere {
    namespace app {

const TaskType Incremental_=1;
const TaskType NonNTFS_=2;
const TaskType History_=4;
const TaskType NTFS_=8;
const TaskType RemobveAble_=16;
const TaskType Full_=Incremental_|NonNTFS_|History_|NTFS_|RemobveAble_;

using SearchComponents = BackgroundModel::SearchComponents;
std::tm time_point_to_tm(const std::chrono::system_clock::time_point& tp) {
    // 1. 将 time_point 转换为 time_t
    std::time_t tt = std::chrono::system_clock::to_time_t(tp);
    
    // 2. 定义 tm 结构体（线程安全存储）
    std::tm local_tm{};
    
    // 3. 调用 localtime_r 填充 tm 结构体
    #ifdef _WIN32
        localtime_s(&local_tm, &tt);   // Windows 平台
    #else
        localtime_r(&tt, &local_tm);   // Linux/macOS 平台
    #endif
    
    return local_tm;
}
TaskScheduler::TaskScheduler(const TaskTable::Task& task, TaskTable& taskTable, PolicyTable& policyTable) 
    : task_(task), taskTable_(taskTable), policyTable_(policyTable),running_(true) {}

QString timeEpochString(const TaskTable::Task& task_,std::tm tm_) {
    // if (task_.InspectionCycle == "everyDay") {
    //     return QString("%1-%2-%3").arg(tm_.tm_year + 1900).arg(tm_.tm_mon + 1).arg(tm_.tm_mday);
    // } else if (task_.InspectionCycle == "weekly") {
    //     return QString("w-%1").arg((std::mktime(&tm_)+88*3600) /(86400*7));
    // } else if (task_.InspectionCycle == "monthly") {
    //     return QString("%1-%2").arg(tm_.tm_year + 1900).arg(tm_.tm_mon + 1);
    // } else {
    //     return QString("%1-%2-%3").arg(tm_.tm_year + 1900).arg(tm_.tm_mon + 1).arg(tm_.tm_mday);
    // }
    return "";
}

bool TaskScheduler::shouldExecute(){
    if (!parseParameters()) {
        return false;
    }
    if (!parsePolicyContent()) {
        return false;
    }

    if (task_.status == TaskTable::TaskStatus::Cancelled) {
        return false;
    }
    return true;
}
bool TaskScheduler::parseParameters() {
    QJsonParseError parseError;
    QJsonDocument doc = QJsonDocument::fromJson(QString::fromLocal8Bit(task_.parameters.c_str()).toUtf8(), &parseError);
    
    if (parseError.error != QJsonParseError::NoError) {
        SPDLOG_ERROR("Failed to parse task parameters: {}", parseError.errorString().toStdString());
        return false;
    }

    if (!doc.isObject()) {
        SPDLOG_ERROR("Invalid task parameters format: not a JSON object");
        return false;
    }
    policy_id_ = "";
    QJsonObject root = doc.object();
    if (root.contains("policyId")) {
        policy_id_ = root["policyId"].toString().toStdString();
        //return true;
    }
    /*"checkItems": [
        "deepCheck"
    ],*/
    if (root.contains("checkItems") && root["checkItems"].isArray()) {
        auto checkItems = root["checkItems"].toArray();
        for (auto item : checkItems) {
            if (item.isString()) {
                if (item.toString() == "deepCheck") {
                    deepCheck_ = true;
                    break;
                }
            }
        }
    }
    if (policy_id_.empty()) {
      SPDLOG_ERROR("Missing required field 'policyId' in task parameters");
      return false;
    }
    return true;
}


bool TaskScheduler::parsePolicyContent() {
    if (policy_id_.empty()) {
        SPDLOG_ERROR("Cannot parse policy content: policy_id_ is empty");
        return false;
    }

    // 获取PolicyTable实例
    auto policy = policyTable_.get(policy_id_);
    if (!policy.has_value()) {
        SPDLOG_ERROR("Failed to get policy with id: {}", policy_id_);
        return false;
    }
    // return false;
    // 解析policy content
    if (!PolicyContent::ParseFromJson(policy.value().content, policyContentRequest_)) {
        SPDLOG_ERROR("Failed to parse policy content for policy: {}", policy_id_);
        return false;
    }

    if (policyContentRequest_.fileCheckSettings.basic.keywordSelects.empty()) {
        SPDLOG_ERROR("Missing required field 'keywordSelects' in policy content");
        return false;
    }
    if (!policyContentRequest_.fileCheckSettings.basic.searchFileContent && !policyContentRequest_.fileCheckSettings.basic.searchFileName) {
        SPDLOG_ERROR("At least one of 'keywordSelects' and'searchFileContent' or'searchFileName' should be true in policy content");
        return false;
    }
    return true;
}

void TaskScheduler::execute_(TaskType taskType) {
    auto orgStatus = task_.status;
    lastSuccessTime_ = std::chrono::system_clock::now();
    if (task_.status != TaskTable::TaskStatus::Running) {
        task_.status = TaskTable::TaskStatus::Running;
        if (task_.lastSuccessTime.has_value()) lastSuccessTime_ = task_.lastSuccessTime.value();
    }
    if (taskType == Full_) {
        taskTable_.updateStatus(task_.taskId, TaskTable::TaskStatus::Running);
    }
    // 生成time_epoch_
    auto now = time_point_to_tm(std::chrono::system_clock::now());
    time_epoch_ = timeEpochString(task_, now);
    {
    std::lock_guard<std::mutex> lock(scheMutex_);
    backgroundModel_ = std::make_shared<BackgroundModel>(task_.taskId);
    nonNTFSSearchProcessor_ = std::make_shared<NonNTFSSearchProcessor>(task_.taskId);
    backgroundHistoryModel_ = std::make_shared<BackgroundHistoryModel>(task_.taskId);
    }

    auto keywords =
        QString::fromLocal8Bit(policyContentRequest_.fileCheckSettings.basic
                                   .keywordSelects.c_str()).trimmed();
    if (policyContentRequest_.fileCheckSettings.basic.keywordRelation == "or") {
        keywords = keywords.replace(",", " ").trimmed();;
    }
    else if  (policyContentRequest_.fileCheckSettings.basic.keywordRelation == "and") {
        keywords = keywords.replace(",", " +").trimmed();
        if (!keywords.startsWith("+")) {
            keywords.prepend("+");
        }
    }

    auto excludeKeywords =
        QString::fromLocal8Bit(policyContentRequest_.fileCheckSettings.basic
                                   .excludeKeywordSelects.c_str()).trimmed();
    if (policyContentRequest_.fileCheckSettings.basic.excludeKeywordRelation == "or") {
        excludeKeywords = excludeKeywords.replace(",", " ").trimmed();;
    }
    else if  (policyContentRequest_.fileCheckSettings.basic.excludeKeywordRelation == "and") {
        excludeKeywords = excludeKeywords.replace(",", " +").trimmed();
        if (!excludeKeywords.startsWith("+")) {
            excludeKeywords.prepend("+");
        }
    }
    auto searchFileContentKeywords = policyContentRequest_.fileCheckSettings.basic.searchFileContent ? keywords : QString();
    auto searchFileNameKeywords = policyContentRequest_.fileCheckSettings.basic.searchFileName ? keywords : QString();
    auto searchFileContentKeywordsExclude =
        policyContentRequest_.fileCheckSettings.basic.searchFileContent
            ? excludeKeywords
            : QString();
    auto searchFileNameKeywordsExclude =
        policyContentRequest_.fileCheckSettings.basic.searchFileName
            ? excludeKeywords
            : QString();
    bool enableCompression = true;//policyContentRequest_.fileCheckSettings.basic.checkArchive;
    FilePathFilter pathFilter;
        if (policyContentRequest_.fileCheckSettings.basic.quick) {
        if (policyContentRequest_.fileCheckSettings.basic.desktop) pathFilter.addDeskTop();
        if (policyContentRequest_.fileCheckSettings.basic.recycle) pathFilter.addRecycleBin();
        if (policyContentRequest_.fileCheckSettings.basic.downloads) pathFilter.addDownloads();
        if (policyContentRequest_.fileCheckSettings.basic.myDocuments) pathFilter.addMyDocuments();
        if (policyContentRequest_.fileCheckSettings.basic.officeBackup) pathFilter.addOfficeBackup();
        if (policyContentRequest_.fileCheckSettings.basic.imTools) pathFilter.addIMTools();
    }
    pathFilter.addCustom(policyContentRequest_.fileCheckSettings.basic.customPaths);
    FileExtFilter extFilter;
    extFilter.addExt(policyContentRequest_.fileCheckSettings.basic.officeTypes);
    extFilter.addExt(policyContentRequest_.fileCheckSettings.basic.versionTypes);
    extFilter.addExt(policyContentRequest_.fileCheckSettings.basic.imageTypes);
    extFilter.addExt(policyContentRequest_.fileCheckSettings.basic.webTypes);
    extFilter.addExt(policyContentRequest_.fileCheckSettings.basic.textTypes);
    extFilter.addExt(policyContentRequest_.fileCheckSettings.basic.archiveTypes);
    extFilter.addCustom(policyContentRequest_.fileCheckSettings.basic.extraTypes);
    ViolationProcessor::instance()->setLockScreen(policyContentRequest_.violationSettings.lockScreen);
    ViolationProcessor::instance()->setDisconnectNetwork(policyContentRequest_.violationSettings.disconnectNetwork, policyContentRequest_.violationSettings.disconnectType);
    ViolationProcessor::instance()->setEjectCD(policyContentRequest_.violationSettings.ejectCd);
    ViolationProcessor::instance()->setEjectUSB(policyContentRequest_.violationSettings.ejectUsb);
    ViolationProcessor::instance()->resetJudgements(policyContentRequest_.judgements);
    backgroundModel_->setFilePathFilter(pathFilter);
    backgroundModel_->setFileExtFilter(extFilter);
    backgroundModel_->updateAdvancedChecker(policyContentRequest_.fileCheckSettings.advanced);
    backgroundModel_->setNetworkAccessManager(networkAccessManager_);
    backgroundModel_->setFileFormatAutoMatch(policyContentRequest_.fileCheckSettings.basic.fileFormatAutoMatch);
    backgroundModel_->setTimeEpoch(time_epoch_);
    if (policyContentRequest_.fileCheckSettings.basic.contentEnhance)
        backgroundModel_->setMaxEmbbedCntPerFile(policyContentRequest_.fileCheckSettings.basic.maxEmbedResourceNum);
    // 为增量检索构建SearchComponents
    auto incrementalSearchComponents = backgroundModel_->buildSearchComponents(
        searchFileNameKeywords, searchFileNameKeywordsExclude,
        searchFileContentKeywords, searchFileContentKeywordsExclude,
        false, // caseInsensive
        policyContentRequest_.fileCheckSettings.advanced.summaryLength,
        policyContentRequest_.fileCheckSettings.advanced.summaryCount
    );

    auto nonNTFSSearchComponents = backgroundModel_->buildSearchComponents(
        searchFileNameKeywords, searchFileNameKeywordsExclude,
        searchFileContentKeywords, searchFileContentKeywordsExclude,
        false, // caseInsensive
        policyContentRequest_.fileCheckSettings.advanced.summaryLength,
        policyContentRequest_.fileCheckSettings.advanced.summaryCount
    );

    // 在移动组件之前先保存查询对象指针
    const Query* parsedFullTextQueryPtr = incrementalSearchComponents.parsedFullTextQuery.get();

    //设置当前任务的增量检索
    if (taskType & Incremental_) {  //增量
        IncrementalSearchManager::instance()->setCurrentTask(
            task_.taskId,
            pathFilter,
            extFilter,
            std::move(incrementalSearchComponents.fileNameMatcher),
            std::move(incrementalSearchComponents.fileNameExcludeMatcher),
            std::move(incrementalSearchComponents.fullTextMatcher),
            std::move(incrementalSearchComponents.fullTextExcludeMatcher),
            std::move(incrementalSearchComponents.filePathHighlighter),
            std::move(incrementalSearchComponents.fullTextHighlighter),
            incrementalSearchComponents.configService,
            incrementalSearchComponents.advancedChecker,
            networkAccessManager_,  // 传递网络访问管理器
            parsedFullTextQueryPtr  // 传递原始查询对象指针
        );
      firstExecAfterStatup_ = false;
    }
    //if (taskType & NonNTFS_) { //非ntfs 和 可移动设备
    //    if ((taskType & RemobveAble_) && taskType != Full_) {
    //        nonNTFSSearchProcessor_->setDrivers(customRoots_);
    //    }
    //    nonNTFSSearchProcessor_->updateSearchParameters(
    //        pathFilter,
    //        extFilter,
    //        std::move(nonNTFSSearchComponents.fileNameMatcher),
    //        std::move(nonNTFSSearchComponents.fileNameExcludeMatcher),
    //        std::move(nonNTFSSearchComponents.fullTextMatcher),
    //        std::move(nonNTFSSearchComponents.fullTextExcludeMatcher),
    //        std::move(nonNTFSSearchComponents.filePathHighlighter),
    //        std::move(nonNTFSSearchComponents.fullTextHighlighter),
    //        nonNTFSSearchComponents.configService,
    //        nonNTFSSearchComponents.advancedChecker,
    //        parsedFullTextQueryPtr  // 传递原始查询对象指针
    //    );
    //    nonNTFSSearchProcessor_->setNetworkAccessManager(networkAccessManager_);
    //    nonNTFSSearchProcessor_->start();
    //    TaskManager::instance()->clearAppendDrivers();
    //}
   
    if (taskType & History_) {
        backgroundHistoryModel_->setCreateTimeChecker(TimeChecker(policyContentRequest_.fileTrace.createTime.start/1000, policyContentRequest_.fileTrace.createTime.end/1000));
        backgroundHistoryModel_->search("", "", true);
    }
    if (taskType & NTFS_) {
        backgroundModel_->search(
                searchFileNameKeywords, searchFileNameKeywordsExclude,
                searchFileContentKeywords, searchFileContentKeywordsExclude, "", true,
                false, false, enableCompression, policyContentRequest_.fileCheckSettings.advanced.summaryCount,
                policyContentRequest_.fileCheckSettings.advanced.summaryLength
            );
    }
    //nonNTFSSearchProcessor_->waitForCompletion();
    if (taskType == Full_) {
        taskTable_.updateLastSuccessTime(task_.taskId, lastSuccessTime_);
    }
    taskTable_.updateStatus(task_.taskId, TaskTable::TaskStatus::Completed);
    std::lock_guard<std::mutex> lock(scheMutex_);
    backgroundModel_.reset();
    nonNTFSSearchProcessor_.reset();;
    backgroundHistoryModel_.reset();;
}

void TaskScheduler::execute() {
    while (true) {
        if (!running_.load()) {
            break; // 停止
        }
         TaskType taskType = 0;
        if (!task_.lastSuccessTime.has_value()) { // 任务一次全量检索也没有执行成功，执行全量
            taskType = Full_;
        } else {
            auto now = std::chrono::system_clock::now();
            auto lastSuccess = task_.lastSuccessTime.value();
            auto duration =
                std::chrono::duration_cast<std::chrono::hours>(now - lastSuccess);
            // 30天 = 30 * 24 = 720小时
            if (duration.count() >= 720) {
            taskType = Full_;
            }
        }
        taskType = taskType | History_;
        if (firstExecAfterStatup_) taskType = taskType | Incremental_;
        customRoots_ = getDrivers();
        if (!customRoots_.empty()) taskType = taskType | RemobveAble_ | NonNTFS_;        
        execute_(taskType);
        auto t_ = taskTable_.get(task_.taskId);
        if (t_.has_value())
          task_ = t_.value();
        else
          return;
        if (!shouldExecute()) {
            return;
        }
        QThread::sleep(2);
    }
}

int TaskScheduler::priority() const {
    return task_.priority;
}

std::string TaskScheduler::taskId() const {
    return task_.taskId;
}

void TaskScheduler::stop() {
  running_.store(false);
  std::lock_guard<std::mutex> lock(scheMutex_);
  if (backgroundModel_ != nullptr) {
        backgroundModel_->close();
  }
  if (nonNTFSSearchProcessor_ != nullptr) {
        nonNTFSSearchProcessor_->stop();
  }
}

std::vector<std::filesystem::path> TaskScheduler::getDrivers() {
    std::vector<std::filesystem::path> drivers;
    auto appendDrivers = TaskManager::instance()->getAppendDrivers();
    if (appendDrivers.empty()) {
        return drivers;
    }
    for (const auto& driver : appendDrivers) {
        drivers.push_back(std::filesystem::path(driver));
    }
    return drivers;
}

}
}
