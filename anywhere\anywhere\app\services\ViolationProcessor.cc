#include "ViolationProcessor.h"
#include <spdlog/spdlog.h>
#include <shared_mutex>
#include "app/windows/QLockScreen.h"
#include "app/windows/QTrayIcon.h"
#include "NetworkService.h"
#include "core/ServiceRegistry.h"
#include "Miscs.h"
namespace anywhere {
namespace app {

ViolationProcessor* ViolationProcessor::instance() {
    static ViolationProcessor instance;
    return &instance;
}

ViolationProcessor::ViolationProcessor(QObject* parent) 
    : QObject(parent),
      lockScreen_(false),
      disconnectNetwork_(false),
      ejectCD_(false),ejectUSB_(false) {
    queryParser_ = core::ServiceRegistry::instance()->getService<QueryParserToken>();
    setupConnections();
}

void ViolationProcessor::setupConnections() {
    connect(this, &ViolationProcessor::lockScreen,
            QLockScreen::instance(), &QLockScreen::lock);
    connect(this, &ViolationProcessor::disconnectNetwork,
        QLockScreen::instance(), &QLockScreen::disconnectNetwork);
    connect(this, &ViolationProcessor::ejectCD,
        QLockScreen::instance(), &QLockScreen::startCDMonitoring);
    connect(this, &ViolationProcessor::ejectUSB,
        QLockScreen::instance(), &QLockScreen::startUSBMonitoring);
    spdlog::info("ViolationProcessor connections established");
}

bool ViolationProcessor::process(QNetworkAccessManager* networkManager, const TaskResultTable::TaskResult& result) {
    QMutexLocker locker(&mutex_);
    if (networkManager == nullptr) return true;
    auto networkService = core::ServiceRegistry::instance()->getService<NetworkServiceToken>();
    if (result.uploaded == 0 && disconnectNetworkType_ == "report") {
      auto ret = networkService->sendTaskResult(result, networkManager);
        if (!ret) {
            if (QLockScreen::instance()->isLocked()) return true;
            return ret;
        }
        // taskResultTable_.updateUploaded(result.id, true);
    };
    if (lockScreen_) {
        if (!QLockScreen::instance()->memoryLock()) return true;
        if (QLockScreen::instance()->isLocked()) return true;
        bool ret = networkService->lock(networkManager);
        if (!ret) {
            spdlog::error("Failed to request lock screen from server");
            QLockScreen::instance()->memoryUnlock();
            return false;
        }
        Q_EMIT QTrayIcon::instance()->violationSignal();
        if (disconnectNetwork_) {
            Q_EMIT disconnectNetwork();
        }
        if (ejectCD_) {
            Q_EMIT ejectCD();
        }
        if (ejectUSB_) {
            Q_EMIT ejectUSB();
        }
        QThread::sleep(10);
        Q_EMIT lockScreen(false);
    }
    return true;
}

void ViolationProcessor::setLockScreen(bool lockScreen) {
    lockScreen_ = lockScreen;
}

void ViolationProcessor::setDisconnectNetwork(bool disconnectNetwork, std::string disconnectType) {
    disconnectNetwork_ = disconnectNetwork;
    if (disconnectNetwork_) {
        lockScreen_ = true;
    }
    disconnectNetworkType_ = disconnectType;
}

void ViolationProcessor::setEjectCD(bool ejectCD) {
    ejectCD_ = ejectCD;
    if (ejectCD_) {
        lockScreen_ = true;
    }
}

void ViolationProcessor::setEjectUSB(bool ejectUSB) {
    ejectUSB_ = ejectUSB;
    if (ejectUSB_) {
        lockScreen_ = true;
    }
}

bool ViolationProcessor::judge(const std::filesystem::path &path, const std::u8string &text) {
    // 使用读锁保护对匹配器的访问
    std::shared_lock<std::shared_mutex> readLock(rwMutex_);

    if (queryFileNameMatcher_.has_value()) {
        auto &matcher = queryFileNameMatcher_.value();
        auto filename = path.filename().u8string();
        if (matcher.matchOnce(filename)) {
            return true;
        }
    }
    if (queryFileContentMatcher_.has_value()) {
        auto &matcher = queryFileContentMatcher_.value();
        if (matcher.matchOnce(text)) {
            return true;
        }
    }
    return false;
}

void ViolationProcessor::resetJudgements(const std::vector<Judgement>& judgements) {
    // 先在写锁外部构建查询字符串，减少锁持有时间
    QString queryFileName;
    QString queryFileContent;
    for (const auto& j : judgements) {
        QString keyword = QString::fromLocal8Bit(j.keyword.c_str());
        keyword = keyword.trimmed();
        if (keyword.isEmpty()) continue;
        if (j.category == "fileName") {
            if (!queryFileName.isEmpty()) {
                queryFileName = queryFileName + " " + QString::fromLocal8Bit(j.keyword.c_str());
            } else {
                queryFileName = QString::fromLocal8Bit(j.keyword.c_str());
            }
        } else {
            if (!queryFileContent.isEmpty()) {
                queryFileContent = queryFileContent + " " + QString::fromLocal8Bit(j.keyword.c_str());
            } else {
                queryFileContent = QString::fromLocal8Bit(j.keyword.c_str());
            }
        }
    }
    queryFileName = queryFileName.trimmed();
    queryFileContent = queryFileContent.trimmed();

    // 构建新的匹配器
    auto newFileNameMatcher = buildQueryMatcher(queryFileName);
    auto newFileContentMatcher = buildQueryMatcher(queryFileContent);

    // 使用写锁保护对匹配器的更新
    {
        std::unique_lock<std::shared_mutex> writeLock(rwMutex_);
        queryFileNameMatcher_ = std::move(newFileNameMatcher);
        queryFileContentMatcher_ = std::move(newFileContentMatcher);
    }

    SPDLOG_INFO("ViolationProcessor judgements updated - fileName: '{}', fileContent: '{}'",
                queryFileName.toStdString(), queryFileContent.toStdString());
}

std::optional<TermsQueryMatcher<AutoMpmEngine>> ViolationProcessor::buildQueryMatcher(const QString& query) {
    auto parsedQuery_ =
    queryParser_->parse(reinterpret_cast<const char8_t *>(
        query.toUtf8().constData()));
    if (parsedQuery_) {
        auto termsQuery =
            dynamic_cast<const TermsQuery *>(parsedQuery_.get());
        if (termsQuery) {
            TermsQueryMatchEngine<AutoMpmEngine> engine(
                termsQuery, true /* 全文匹配总是不区分大小写的 */);
            return std::make_optional<TermsQueryMatcher<AutoMpmEngine>>(
                engine.matcher());
        }
    }
    return std::optional<TermsQueryMatcher<AutoMpmEngine>>();
}

std::string ViolationProcessor::getViolationTags() {
    // 使用读锁保护对配置变量的访问（虽然这些是简单类型，但为了一致性）
    std::shared_lock<std::shared_mutex> readLock(rwMutex_);

    std::string tags;
    if (lockScreen_) {
        tags += "lockScreen";
    }
    if (disconnectNetwork_) {
        if (!tags.empty()) {
            tags += ",";
        }
        tags += "disconnectNetwork";
    }
    return tags;
}

} // namespace app
} // namespace anywhere
