#pragma once

#include <QObject>
#include <QMutex>
#include <shared_mutex>
#include "app/windows/QLockScreen.h"
#include <QNetworkAccessManager>
#include "QueryParser.h"
#include "PolicyContent.h"
#include <filesystem>
#include "TermsQueryMatchEngine.h"
#include "mpm/AutoMpmEngine.h"
#include "database/TaskResultTable.h"
namespace anywhere {
namespace app {

class ViolationProcessor : public QObject {
    Q_OBJECT

public:
    static ViolationProcessor* instance();
    bool judge(const std::filesystem::path &path, const std::u8string &text);
    bool process(QNetworkAccessManager* networkManager, const TaskResultTable::TaskResult& result);
    void setLockScreen(bool lockScreen);
    void setDisconnectNetwork(bool disconnectNetwork, std::string disconnectType);
    void setEjectCD(bool ejectCD);
    void setEjectUSB(bool ejectUSB);
    void resetJudgements(const std::vector<Judgement>& judgements);
    std::string getViolationTags();
Q_SIGNALS:
    void lockScreen(bool);
    void disconnectNetwork();
    void ejectCD();
    void ejectUSB();

private:
    explicit ViolationProcessor(QObject* parent = nullptr);
    ~ViolationProcessor() = default;
    ViolationProcessor(const ViolationProcessor&) = delete;
    ViolationProcessor& operator=(const ViolationProcessor&) = delete;
    std::optional<TermsQueryMatcher<AutoMpmEngine>> buildQueryMatcher(const QString& query);
    std::optional<TermsQueryMatcher<AutoMpmEngine>> queryFileNameMatcher_;
    std::optional<TermsQueryMatcher<AutoMpmEngine>> queryFileContentMatcher_;
    void setupConnections();
    bool lockScreen_;
    bool disconnectNetwork_;
    bool ejectCD_;
    bool ejectUSB_;
    std::string disconnectNetworkType_;
    std::shared_ptr<QueryParser> queryParser_;
    QMutex mutex_;  // 用于process()方法的互斥锁
    mutable std::shared_mutex rwMutex_;  // 用于judge()和resetJudgements()的读写锁
};

} // namespace app
} // namespace anywhere
