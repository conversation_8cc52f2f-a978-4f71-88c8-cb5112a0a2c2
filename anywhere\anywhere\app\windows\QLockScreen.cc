#include "QLockScreen.h"
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QQuickItem>
#include <QGuiApplication>
#include <QMessageBox>
#include "../services/GlobalMouseEvent.h"
#include "../services/GlobalKeyEvent.h"
#include <QStandardPaths>
#include <QDir>
#include <QMutexLocker>
#include "../services/NetworkService.h"
#include "../services/NetworkAdapterController.h"
#include <tlhelp32.h>
#include <sddl.h>
#include <shellapi.h>
#include <winioctl.h>  // 用于IOCTL_STORAGE_*常量
#include <ntddstor.h>  // 用于存储设备相关结构体
#include <QtConcurrent/QtConcurrentRun>
namespace anywhere {
namespace app {
const static QString password = "1234";
QLockScreen* QLockScreen::instance() {
    static QLockScreen instance;
    return &instance;
}

QLockScreen::QLockScreen(QObject* parent) : QObject(parent),
    m_db((QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/system.db").toStdString()),
    m_systemInfoTable(m_db),m_winlogonPid((DWORD)-2),
    m_cdMonitorThread(nullptr), m_cdMonitoringActive(false),
    m_usbMonitorThread(nullptr), m_usbMonitoringActive(false) {
    // connect(this, &QLockScreen::sigRestoreCtrlAltDel, this, &QLockScreen::RestoreCtrlAltDel);
    // connect(this, &QLockScreen::sigRestoreNetwork, this, &QLockScreen::restoreNetwork);
    // connect(this, &QLockScreen::sigStopCDMonitoring, this, &QLockScreen::stopCDMonitoring);
}

QLockScreen::~QLockScreen() {
}

bool QLockScreen::memoryLock() {
    QMutexLocker locker(&m_mutex);
    if (!memoryLock_){
        memoryLock_ = true;
        return memoryLock_;
    }
    return false;
}

void QLockScreen::memoryUnlock() {
    QMutexLocker locker(&m_mutex);
    memoryLock_ = false;
}

bool QLockScreen::isLocked() {
    QMutexLocker locker(&m_mutex);
    auto value = m_systemInfoTable.getValue("lock_screen");
    return value.has_value() && *value != "0";
}

void QLockScreen::lock(bool startUp) {
    QMutexLocker locker(&m_mutex);
    auto value = m_systemInfoTable.getValue("lock_screen");
    bool needLock =
        (!startUp) ? (!value || *value != "1") : (value && *value == "1");
    if (needLock) {
      DisableCtrlAltDel();
      GlobalMouseEvent::getInstance()->installMouseEvent();
      GlobalKeyEvent::getInstance()->installKeyEvent();
      m_systemInfoTable.insertOrUpdate("lock_screen", "1", "Noticed");

      // 读取管理员信息
      auto realNameValue = m_systemInfoTable.getValue("admin_real_name");
      auto departmentValue = m_systemInfoTable.getValue("admin_department_name");
      auto phoneValue = m_systemInfoTable.getValue("admin_phone");
      auto landlineValue = m_systemInfoTable.getValue("admin_landline");

      QString realName = realNameValue.has_value() ? QString::fromLocal8Bit(realNameValue->c_str()) : "";
      QString department = departmentValue.has_value() ? QString::fromLocal8Bit(departmentValue->c_str()) : "";
      QString phone = phoneValue.has_value() ? QString::fromLocal8Bit(phoneValue->c_str()) : "";
      QString landline = landlineValue.has_value() ? QString::fromLocal8Bit(landlineValue->c_str()) : "";

      // 发送带有管理员信息的锁屏信号
      if (!realName.isEmpty() || !department.isEmpty() || !phone.isEmpty() || !landline.isEmpty()) {
        Q_EMIT lockRequestedWithAdminInfo(realName, department, phone, landline);
      } else {
        Q_EMIT lockRequested();
      }
    }
    QtConcurrent::run([this, startUp]() {
    if (startUp) {
      auto value = m_systemInfoTable.getValue("disconnect_network");
      if (value.has_value() && *value != "0") {
        disconnectNetwork();
      }
      value = m_systemInfoTable.getValue("ejectCD");
      if (value.has_value() && *value != "0") {
        startCDMonitoring();
      }
      value = m_systemInfoTable.getValue("ejectUSB");
      if (value.has_value() && *value != "0") {
        startUSBMonitoring();
      }
    }
    });
}

void QLockScreen::unlock(const QString &passwd) {
    QMutexLocker locker(&m_mutex);
    auto value = m_systemInfoTable.getValue("lock_screen");
    if (value.has_value() && *value != "0") {
      auto password_ = m_systemInfoTable.getValue("unLockedKey");
      auto p = QString::fromLocal8Bit(
          password_.has_value() ? password_.value().c_str() : "");
      if (p.isEmpty())
        p = password;
      if (passwd == p) {
        GlobalMouseEvent::getInstance()->removeMouseEvent();
        GlobalKeyEvent::getInstance()->removeKeyEvent();
        Q_EMIT unlockSuccess();
        RestoreCtrlAltDel();
        QtConcurrent::run([this, passwd]() {
        restoreNetwork();
        stopCDMonitoring();
        stopUSBMonitoring();
        m_systemInfoTable.insertOrUpdate("unLockedKey", "", "unLockedKey");
        m_systemInfoTable.insertOrUpdate("lock_screen", "0", "NotNoticed");
        memoryUnlock();
        });
      } else {
        Q_EMIT unlockFailed();
      }
    }
    //});
}
bool QLockScreen::EnableDebugPrivilege() {
    HANDLE hToken;
    TOKEN_PRIVILEGES tp;
    LUID luid;

    if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hToken)) {
        return false;
    }

    if (!LookupPrivilegeValue(NULL, SE_DEBUG_NAME, &luid)) {
        CloseHandle(hToken);
        return false;
    }

    tp.PrivilegeCount = 1;
    tp.Privileges[0].Luid = luid;
    tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

    if (!AdjustTokenPrivileges(hToken, FALSE, &tp, sizeof(TOKEN_PRIVILEGES), NULL, NULL)) {
        CloseHandle(hToken);
        return false;
    }

    if (GetLastError() == ERROR_NOT_ALL_ASSIGNED) {
        CloseHandle(hToken);
        return false;
    }

    CloseHandle(hToken);
    return true;
}

void QLockScreen::DisableCtrlAltDel()
{
    m_winlogonPid = GetWinLogonPid();
    if (m_winlogonPid != (DWORD)-1)
    {
        SuspendWinlogonThreads(true);
    }
}

void QLockScreen::RestoreCtrlAltDel()
{
    if (m_winlogonPid != (DWORD)-1 && m_winlogonPid != (DWORD)-2)
    {
        SuspendWinlogonThreads(false);
    }
}

DWORD QLockScreen::GetWinLogonPid()
{
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE)
        return (DWORD)-1;

    PROCESSENTRY32 pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32);

    if (Process32First(hSnapshot, &pe32))
    {
        do
        {
            // 简单的字符串比较
            bool isWinlogon = true;
            const wchar_t* target = L"winlogon.exe";
            for (int i = 0; target[i] != L'\0'; i++)
            {
                if (pe32.szExeFile[i] != target[i] &&
                    pe32.szExeFile[i] != (target[i] - L'a' + L'A')) // 简单的大小写转换
                {
                    isWinlogon = false;
                    break;
                }
            }
            if (isWinlogon)
            {
                CloseHandle(hSnapshot);
                return pe32.th32ProcessID;
            }
        } while (Process32Next(hSnapshot, &pe32));
    }

    CloseHandle(hSnapshot);
    return (DWORD)-1;
}

int QLockScreen::SuspendWinlogonThreads(bool suspend)
{
    if (m_winlogonPid == 0 || m_winlogonPid == (DWORD)-1)
        return -1;

    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPTHREAD, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE)
        return -1;

    THREADENTRY32 te32;
    te32.dwSize = sizeof(THREADENTRY32);

    int result = 0;
    if (Thread32First(hSnapshot, &te32))
    {
        do
        {
            if (te32.th32OwnerProcessID == m_winlogonPid)
            {
                HANDLE hThread = OpenThread(THREAD_SUSPEND_RESUME, FALSE, te32.th32ThreadID);
                if (hThread)
                {
                    if (suspend)
                    {
                        // 挂起线程
                        DWORD suspendCount = SuspendThread(hThread);
                        if (suspendCount != (DWORD)-1)
                        {
                            m_suspendedThreads.push_back(hThread);
                            result++;
                        }
                        else
                        {
                            CloseHandle(hThread);
                        }
                    }
                    else
                    {
                        // 这里不需要处理，因为恢复时会用另一种方法
                        CloseHandle(hThread);
                    }
                }
            }
        } while (Thread32Next(hSnapshot, &te32));
    }

    CloseHandle(hSnapshot);

    // 如果是恢复操作，恢复所有挂起的线程
    if (!suspend)
    {
        for (HANDLE hThread : m_suspendedThreads)
        {
            ResumeThread(hThread);
            CloseHandle(hThread);
        }
        m_suspendedThreads.clear();
        result = 0;
    }

    return result;
}

void QLockScreen::forceActivateWindow(HWND hwnd) {
    ShowWindow(hwnd, SW_RESTORE);  // 恢复窗口
    SetWindowPos(hwnd, HWND_TOPMOST, 0,0,0,0, SWP_NOSIZE|SWP_NOMOVE);
    SetWindowPos(hwnd, HWND_NOTOPMOST,0,0,0,0, SWP_NOSIZE|SWP_NOMOVE);
    SetForegroundWindow(hwnd);     // 强制激活
}

void QLockScreen::disconnectNetwork() {
    NetworkAdapterController::instance().disableAllNetworks();
    m_systemInfoTable.insertOrUpdate("disconnect_network", "1", "disconnect network");
}

void QLockScreen::restoreNetwork() {
    auto value = m_systemInfoTable.getValue("disconnect_network");
    if (value.has_value() && *value != "0")
    {
        NetworkAdapterController::instance().enableAllNetworks();
        m_systemInfoTable.insertOrUpdate("disconnect_network", "0", "disconnect network");
    }
}

void QLockScreen::startCDMonitoring() {
    //QMutexLocker locker(&m_mutex);

    // 如果已经在监控，先停止
    if (m_cdMonitoringActive) {
        return;
    }

    // 立即弹出所有已插入的CD/DVD
    EjectAllCDDrives();

    // 启动监控线程
    m_cdMonitoringActive = true;
    m_cdMonitorThread = CreateThread(
        nullptr,                    // 默认安全属性
        0,                         // 默认堆栈大小
        CDMonitorThreadProc,       // 线程函数
        this,                      // 传递this指针
        0,                         // 立即运行
        nullptr                    // 不需要线程ID
    );

    if (m_cdMonitorThread == nullptr) {
        m_cdMonitoringActive = false;
        // 可以添加错误日志
        return;
    }
    m_systemInfoTable.insertOrUpdate("ejectCD", "1", "ejectCD");
}

void QLockScreen::stopCDMonitoring() {
    //QMutexLocker locker(&m_mutex);

    // 停止监控
    m_cdMonitoringActive = false;

    // 等待监控线程结束
    if (m_cdMonitorThread != nullptr) {
        // 等待线程结束，最多等待5秒
        WaitForSingleObject(m_cdMonitorThread, 5000);
        CloseHandle(m_cdMonitorThread);
        m_cdMonitorThread = nullptr;
    }
    m_systemInfoTable.insertOrUpdate("ejectCD", "0", "ejectCD");
}

void QLockScreen::EjectAllCDDrives() {
    // 获取系统中所有驱动器
    DWORD drives = GetLogicalDrives();
    char driveLetter = 'A';

    for (int i = 0; i < 26; i++) {
        if (drives & (1 << i)) {
            char drivePath[4] = {driveLetter, ':', '\\', '\0'};

            // 检查是否为CD/DVD驱动器
            UINT driveType = GetDriveTypeA(drivePath);
            if (driveType == DRIVE_CDROM) {
                EjectSingleCDDrive(driveLetter);
            }
        }
        driveLetter++;
    }
}

bool QLockScreen::EjectSingleCDDrive(char driveLetter) {
    // 构造正确的设备路径格式：\\.\X:
    char devicePath[8];
    sprintf_s(devicePath, sizeof(devicePath), "\\\\.\\%c:", driveLetter);

    // 尝试不同的访问权限组合
    DWORD accessModes[] = {
        GENERIC_READ | GENERIC_WRITE,  // 首选：读写权限
        GENERIC_READ,                  // 备选：只读权限
        0                             // 最后尝试：无特定权限
    };

    for (int i = 0; i < 3; i++) {
        HANDLE hDevice = CreateFileA(
            devicePath,
            accessModes[i],
            FILE_SHARE_READ | FILE_SHARE_WRITE,
            nullptr,
            OPEN_EXISTING,
            0,
            nullptr
        );

        if (hDevice != INVALID_HANDLE_VALUE) {
            DWORD bytesReturned;

            // 首先尝试解锁媒体（如果被锁定）
            DeviceIoControl(
                hDevice,
                IOCTL_STORAGE_MEDIA_REMOVAL,
                nullptr, 0,
                nullptr, 0,
                &bytesReturned,
                nullptr
            );

            // 发送弹出命令
            BOOL result = DeviceIoControl(
                hDevice,
                IOCTL_STORAGE_EJECT_MEDIA,
                nullptr, 0,
                nullptr, 0,
                &bytesReturned,
                nullptr
            );

            CloseHandle(hDevice);
            return result != FALSE;
        }
    }

    // 所有尝试都失败
    return false;
}

bool QLockScreen::IsCDInserted(char driveLetter) {
    // 构造正确的设备路径格式：\\.\X:
    char devicePath[8];
    sprintf_s(devicePath, sizeof(devicePath), "\\\\.\\%c:", driveLetter);

    // 尝试以只读方式打开设备
    HANDLE hDevice = CreateFileA(
        devicePath,
        GENERIC_READ,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        nullptr,
        OPEN_EXISTING,
        0,
        nullptr
    );

    if (hDevice == INVALID_HANDLE_VALUE) {
        return false;  // 无法打开设备
    }

    // 检查媒体是否存在
    DWORD bytesReturned;
    BOOL result = DeviceIoControl(
        hDevice,
        IOCTL_STORAGE_CHECK_VERIFY,
        nullptr, 0,
        nullptr, 0,
        &bytesReturned,
        nullptr
    );

    CloseHandle(hDevice);
    return result != FALSE;
}

DWORD WINAPI QLockScreen::CDMonitorThreadProc(LPVOID lpParam) {
    QLockScreen* pThis = static_cast<QLockScreen*>(lpParam);

    while (pThis->m_cdMonitoringActive) {
        // 检查所有CD/DVD驱动器
        DWORD drives = GetLogicalDrives();
        char driveLetter = 'A';

        for (int i = 0; i < 26; i++) {
            if (!pThis->m_cdMonitoringActive) break;

            if (drives & (1 << i)) {
                char drivePath[4] = {driveLetter, ':', '\\', '\0'};

                // 检查是否为CD/DVD驱动器
                UINT driveType = GetDriveTypeA(drivePath);
                if (driveType == DRIVE_CDROM) {
                    // 检查是否有光盘插入，如果有则弹出
                    if (pThis->IsCDInserted(driveLetter)) {
                        pThis->EjectSingleCDDrive(driveLetter);
                    }
                }
            }
            driveLetter++;
        }

        // 每500毫秒检查一次
        Sleep(500);
    }

    return 0;
}

// USB存储设备监控相关实现
void QLockScreen::startUSBMonitoring() {
    if (m_usbMonitoringActive) {
        return; // 已经在监控中
    }

    // 先弹出所有已连接的USB存储设备
    EjectAllUSBStorageDevices();

    // 启动监控线程
    m_usbMonitoringActive = true;
    m_usbMonitorThread = CreateThread(
        nullptr,                    // 默认安全属性
        0,                         // 默认堆栈大小
        USBMonitorThreadProc,      // 线程函数
        this,                      // 传递this指针
        0,                         // 立即运行
        nullptr                    // 不需要线程ID
    );

    if (m_usbMonitorThread == nullptr) {
        m_usbMonitoringActive = false;
        return;
    }
    m_systemInfoTable.insertOrUpdate("ejectUSB", "1", "ejectUSB");
}

void QLockScreen::stopUSBMonitoring() {
    if (!m_usbMonitoringActive) {
        return; // 没有在监控
    }

    // 停止监控
    m_usbMonitoringActive = false;

    // 等待监控线程结束
    if (m_usbMonitorThread != nullptr) {
        // 等待线程结束，最多等待5秒
        WaitForSingleObject(m_usbMonitorThread, 5000);
        CloseHandle(m_usbMonitorThread);
        m_usbMonitorThread = nullptr;
    }
    m_systemInfoTable.insertOrUpdate("ejectUSB", "0", "ejectUSB");
}

DWORD WINAPI QLockScreen::USBMonitorThreadProc(LPVOID lpParam) {
    QLockScreen* pThis = static_cast<QLockScreen*>(lpParam);

    while (pThis->m_usbMonitoringActive) {
        // 获取当前所有USB存储设备
        auto usbDevices = pThis->GetUSBStorageDevices();

        // 弹出所有检测到的USB存储设备
        for (const auto& devicePath : usbDevices) {
            pThis->EjectUSBStorageDevice(devicePath);
        }

        // 每秒检查一次
        Sleep(1000);
    }

    return 0;
}

void QLockScreen::EjectAllUSBStorageDevices() {
    auto usbDevices = GetUSBStorageDevices();
    for (const auto& devicePath : usbDevices) {
        EjectUSBStorageDevice(devicePath);
    }
}

bool QLockScreen::EjectUSBStorageDevice(const std::wstring& devicePath) {
    HANDLE hDevice = CreateFileW(devicePath.c_str(),
                                GENERIC_READ | GENERIC_WRITE,
                                FILE_SHARE_READ | FILE_SHARE_WRITE,
                                nullptr, OPEN_EXISTING, 0, nullptr);

    if (hDevice == INVALID_HANDLE_VALUE) {
        return false;
    }

    DWORD bytesReturned;
    bool result = DeviceIoControl(hDevice, IOCTL_STORAGE_EJECT_MEDIA,
                                 nullptr, 0, nullptr, 0, &bytesReturned, nullptr);

    CloseHandle(hDevice);
    return result;
}

bool QLockScreen::IsUSBStorageDevice(const std::wstring& devicePath) {
    HANDLE hDevice = CreateFileW(devicePath.c_str(),
                                0, FILE_SHARE_READ | FILE_SHARE_WRITE,
                                nullptr, OPEN_EXISTING, 0, nullptr);

    if (hDevice == INVALID_HANDLE_VALUE) {
        return false;
    }

    STORAGE_DEVICE_DESCRIPTOR* pDeviceDescriptor = nullptr;
    STORAGE_PROPERTY_QUERY query = {};
    query.PropertyId = StorageDeviceProperty;
    query.QueryType = PropertyStandardQuery;

    DWORD bytesReturned;
    STORAGE_DESCRIPTOR_HEADER header = {};

    bool result = DeviceIoControl(hDevice, IOCTL_STORAGE_QUERY_PROPERTY,
                                 &query, sizeof(query),
                                 &header, sizeof(header),
                                 &bytesReturned, nullptr);

    if (result && header.Size > 0) {
        pDeviceDescriptor = (STORAGE_DEVICE_DESCRIPTOR*)malloc(header.Size);
        if (pDeviceDescriptor) {
            result = DeviceIoControl(hDevice, IOCTL_STORAGE_QUERY_PROPERTY,
                                   &query, sizeof(query),
                                   pDeviceDescriptor, header.Size,
                                   &bytesReturned, nullptr);

            if (result) {
                // 检查是否为USB设备且为可移动存储设备
                bool isUSB = (pDeviceDescriptor->BusType == BusTypeUsb);
                bool isRemovable = pDeviceDescriptor->RemovableMedia;

                free(pDeviceDescriptor);
                CloseHandle(hDevice);
                return isUSB && isRemovable;
            }
            free(pDeviceDescriptor);
        }
    }

    CloseHandle(hDevice);
    return false;
}

std::vector<std::wstring> QLockScreen::GetUSBStorageDevices() {
    std::vector<std::wstring> usbDevices;

    // 获取所有逻辑驱动器
    DWORD drives = GetLogicalDrives();

    for (int i = 0; i < 26; i++) {
        if (drives & (1 << i)) {
            wchar_t driveLetter = L'A' + i;
            std::wstring drivePath = std::wstring(1, driveLetter) + L":\\";

            // 检查驱动器类型
            UINT driveType = GetDriveTypeW(drivePath.c_str());
            if (driveType == DRIVE_REMOVABLE) {
                // 构建设备路径
                std::wstring devicePath = L"\\\\.\\" + std::wstring(1, driveLetter) + L":";

                // 验证是否为USB存储设备
                if (IsUSBStorageDevice(devicePath)) {
                    usbDevices.push_back(devicePath);
                }
            }
        }
    }

    return usbDevices;
}

} // namespace app
} // namespace anywhere
