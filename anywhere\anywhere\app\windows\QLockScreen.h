#pragma once

#include <QObject>
#include <QMutex>
#include "database/SystemInfoTable.h"
#include "database/Database.h"
#include <windows.h>
namespace anywhere {
namespace app {

class QLockScreen : public QObject {
    Q_OBJECT
    // Q_PROPERTY(bool locked READ isLocked NOTIFY lockedChanged)

public:
    static QLockScreen* instance();
    bool isLocked();
    // bool isLocked() const;
    bool EnableDebugPrivilege();
    bool memoryLock();
    void memoryUnlock();
public Q_SLOTS:
    Q_INVOKABLE void lock(bool startUp=false);
    Q_INVOKABLE void unlock(const QString& passwd);
    Q_INVOKABLE void forceActivateWindow(HWND hwnd);
    Q_INVOKABLE void disconnectNetwork();
    Q_INVOKABLE void restoreNetwork();
    Q_INVOKABLE void startCDMonitoring();  // 启动CD/DVD监控并弹出已插入的光盘
    Q_INVOKABLE void stopCDMonitoring();   // 停止CD/DVD监控
    Q_INVOKABLE void startUSBMonitoring(); // 启动USB存储设备监控并弹出已连接的USB存储设备
    Q_INVOKABLE void stopUSBMonitoring();  // 停止USB存储设备监控

Q_SIGNALS:
    void lockRequested();
    void lockRequestedWithAdminInfo(const QString& realName, const QString& department,
                                   const QString& phone, const QString& landline);
    // void lockedChanged(bool locked);
    void unlockSuccess();
    void unlockFailed();
    // void sigRestoreCtrlAltDel();
    // void sigRestoreNetwork();
    // void sigStopCDMonitoring();

private:
    explicit QLockScreen(QObject* parent = nullptr);
    ~QLockScreen() override;
    void DisableCtrlAltDel();
    void RestoreCtrlAltDel();
    Q_DISABLE_COPY(QLockScreen)
    DWORD GetWinLogonPid();
    int SuspendWinlogonThreads(bool suspend);
    Database m_db;
    SystemInfoTable m_systemInfoTable;
    QMutex m_mutex;
    DWORD m_winlogonPid;            // Winlogon进程ID
    std::vector<HANDLE> m_suspendedThreads;

    // CD/DVD监控相关
    HANDLE m_cdMonitorThread;       // CD监控线程句柄
    bool m_cdMonitoringActive;      // 监控状态标志
    static DWORD WINAPI CDMonitorThreadProc(LPVOID lpParam); // 监控线程函数
    void EjectAllCDDrives();        // 弹出所有CD/DVD驱动器
    bool EjectSingleCDDrive(char driveLetter); // 弹出单个CD/DVD驱动器
    bool IsCDInserted(char driveLetter);        // 检查光盘是否插入

    // USB存储设备监控相关
    HANDLE m_usbMonitorThread;      // USB监控线程句柄
    bool m_usbMonitoringActive;     // USB监控状态标志
    static DWORD WINAPI USBMonitorThreadProc(LPVOID lpParam); // USB监控线程函数
    void EjectAllUSBStorageDevices(); // 弹出所有USB存储设备
    bool EjectUSBStorageDevice(const std::wstring& devicePath); // 弹出单个USB存储设备
    bool IsUSBStorageDevice(const std::wstring& devicePath); // 检查是否为USB存储设备
    std::vector<std::wstring> GetUSBStorageDevices(); // 获取所有USB存储设备
    bool memoryLock_ = false;
};

} // namespace anywhere::app
} // namespace anywhere
