#include "app/services/DeviceChangeNotifier.h"
#include "Log.h"
#include <iostream>
#include <thread>
#include <chrono>

using namespace anywhere::app;

int main() {
    // 初始化日志
    spdlog::set_level(spdlog::level::debug);
    
    std::cout << "Starting DeviceChangeNotifier test..." << std::endl;
    SPDLOG_INFO("DeviceChangeNotifier test started");
    
    // 创建设备变更通知器
    DeviceChangeNotifier notifier;
    
    // 设置回调
    notifier.setCallback([]() {
        std::cout << "*** DEVICE CHANGE DETECTED! ***" << std::endl;
        SPDLOG_INFO("*** DEVICE CHANGE CALLBACK TRIGGERED ***");
    });
    
    // 启动通知器
    if (!notifier.start()) {
        std::cout << "Failed to start DeviceChangeNotifier" << std::endl;
        SPDLOG_ERROR("Failed to start DeviceChangeNotifier");
        return 1;
    }
    
    std::cout << "DeviceChangeNotifier started successfully." << std::endl;
    std::cout << "Please insert or remove a USB device to test..." << std::endl;
    std::cout << "Press Ctrl+C to exit." << std::endl;
    
    SPDLOG_INFO("DeviceChangeNotifier started, waiting for device changes...");
    
    // 保持程序运行
    try {
        while (true) {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    } catch (...) {
        std::cout << "Exiting..." << std::endl;
    }
    
    notifier.stop();
    SPDLOG_INFO("DeviceChangeNotifier test completed");
    return 0;
}
